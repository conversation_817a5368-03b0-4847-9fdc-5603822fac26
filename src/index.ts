import QRCodeStyling from "./core/QRCodeStyling";
import dotTypes from "./constants/dotTypes";
import cornerDotTypes from "./constants/cornerDotTypes";
import cornerSquareTypes from "./constants/cornerSquareTypes";
import errorCorrectionLevels from "./constants/errorCorrectionLevels";
import errorCorrectionPercents from "./constants/errorCorrectionPercents";
import modes from "./constants/modes";
import qrTypes from "./constants/qrTypes";
import drawTypes from "./constants/drawTypes";
import shapeTypes from "./constants/shapeTypes";
import gradientTypes from "./constants/gradientTypes";

// PIX Module exports
export * as PIX from "./pix";
export {
  PixBRCodeGenerator,
  PixKeyValidator,
  PixFormatter,
  CRC16Calculator,
  generatePixBRCode,
  validatePixKey,
  parsePixBRCode,
  calculateCRC16,
  validate<PERSON><PERSON><PERSON>,
  PIX_CONSTANTS,
  P<PERSON>_KEY_TYPES,
  PIX_VALIDATION_MESSAGES,
  PIX_MODULE_INFO
} from "./pix";

export * from "./types";

export {
  dotTypes,
  cornerDotTypes,
  cornerSquareTypes,
  errorCorrectionLevels,
  errorCorrectionPercents,
  modes,
  qrTypes,
  drawTypes,
  shapeTypes,
  gradientTypes
};

export default QRCodeStyling;
