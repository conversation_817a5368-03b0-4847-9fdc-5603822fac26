import PixFormatter from "./PixFormatter";

describe("PixFormatter", () => {
  describe("Text Formatting", () => {
    it("should remove accents from text", () => {
      const result = PixFormatter.formatText("<PERSON>");
      expect(result).toBe("<PERSON><PERSON>");
    });

    it("should remove special characters", () => {
      const result = PixFormatter.formatText("Test & Company <>");
      expect(result).toBe("Test  Company");
    });

    it("should keep allowed characters", () => {
      const result = PixFormatter.formatText("Test$@%*+-./:_ 123");
      expect(result).toBe("Test$@%*+-./:_ 123");
    });

    it("should handle empty string", () => {
      const result = PixFormatter.formatText("");
      expect(result).toBe("");
    });

    it("should handle null input", () => {
      const result = PixFormatter.formatText(null as any);
      expect(result).toBe("");
    });

    it("should trim whitespace", () => {
      const result = PixFormatter.formatText("  test  ");
      expect(result).toBe("test");
    });

    it("should handle complex accented text", () => {
      const result = PixFormatter.formatText("Ação Ñoño Çedilha");
      expect(result).toBe("Acao Nono Cedilha");
    });
  });

  describe("TLV Value Generation", () => {
    it("should create correct TLV format", () => {
      const result = PixFormatter.getValue("01", "test");
      expect(result).toBe("0104test");
    });

    it("should pad tag to 2 digits", () => {
      const result = PixFormatter.getValue("1", "test");
      expect(result).toBe("0104test");
    });

    it("should handle long values", () => {
      const longValue = "a".repeat(99);
      const result = PixFormatter.getValue("01", longValue);
      expect(result).toBe(`0199${longValue}`);
    });

    it("should handle empty value", () => {
      const result = PixFormatter.getValue("01", "");
      expect(result).toBe("0100");
    });

    it("should handle empty tag", () => {
      const result = PixFormatter.getValue("", "test");
      expect(result).toBe("0004test");
    });

    it("should handle both empty", () => {
      const result = PixFormatter.getValue("", "");
      expect(result).toBe("0000");
    });
  });

  describe("Amount Formatting", () => {
    it("should format positive amount", () => {
      const result = PixFormatter.formatAmount(123.45);
      expect(result).toBe("123.45");
    });

    it("should format integer amount", () => {
      const result = PixFormatter.formatAmount(100);
      expect(result).toBe("100.00");
    });

    it("should handle zero amount", () => {
      const result = PixFormatter.formatAmount(0);
      expect(result).toBe("");
    });

    it("should handle negative amount", () => {
      const result = PixFormatter.formatAmount(-10);
      expect(result).toBe("");
    });

    it("should format decimal with many places", () => {
      const result = PixFormatter.formatAmount(123.456789);
      expect(result).toBe("123.46");
    });
  });

  describe("PIX Key Normalization", () => {
    it("should normalize CPF", () => {
      const result = PixFormatter.normalizePixKey("cpf", "111.444.777-35");
      expect(result).toBe("11144477735");
    });

    it("should normalize phone with country code", () => {
      const result = PixFormatter.normalizePixKey("phone", "(11) 98765-4321");
      expect(result).toBe("+5511987654321");
    });

    it("should add country code to phone", () => {
      const result = PixFormatter.normalizePixKey("phone", "11987654321");
      expect(result).toBe("+5511987654321");
    });

    it("should not duplicate country code", () => {
      const result = PixFormatter.normalizePixKey("phone", "5511987654321");
      expect(result).toBe("+5511987654321");
    });

    it("should not duplicate + prefix", () => {
      const result = PixFormatter.normalizePixKey("phone", "+5511987654321");
      expect(result).toBe("+5511987654321");
    });

    it("should normalize email to lowercase", () => {
      const result = PixFormatter.normalizePixKey("email", "<EMAIL>");
      expect(result).toBe("<EMAIL>");
    });

    it("should trim random key", () => {
      const result = PixFormatter.normalizePixKey("random", "  random-key  ");
      expect(result).toBe("random-key");
    });

    it("should handle unknown key type", () => {
      const result = PixFormatter.normalizePixKey("unknown", "  test  ");
      expect(result).toBe("test");
    });
  });

  describe("Field Formatting", () => {
    it("should format receiver name", () => {
      const result = PixFormatter.formatReceiverName("João da Silva Sauro");
      expect(result).toBe("Joao da Silva Sauro");
    });

    it("should truncate long receiver name", () => {
      const longName = "a".repeat(30);
      const result = PixFormatter.formatReceiverName(longName);
      expect(result).toBe("a".repeat(25));
    });

    it("should format receiver city", () => {
      const result = PixFormatter.formatReceiverCity("São Paulo");
      expect(result).toBe("Sao Paulo");
    });

    it("should truncate long receiver city", () => {
      const longCity = "a".repeat(20);
      const result = PixFormatter.formatReceiverCity(longCity);
      expect(result).toBe("a".repeat(15));
    });

    it("should format reference", () => {
      const result = PixFormatter.formatReference("REF-123");
      expect(result).toBe("REF123");
    });

    it("should use default reference for empty", () => {
      const result = PixFormatter.formatReference("");
      expect(result).toBe("***");
    });

    it("should truncate long reference", () => {
      const longRef = "a".repeat(30);
      const result = PixFormatter.formatReference(longRef);
      expect(result).toBe("a".repeat(25));
    });

    it("should format description", () => {
      const result = PixFormatter.formatDescription("Pagamento de serviços");
      expect(result).toBe("Pagamento de servicos");
    });

    it("should truncate long description", () => {
      const longDesc = "a".repeat(60);
      const result = PixFormatter.formatDescription(longDesc);
      expect(result).toBe("a".repeat(50));
    });
  });

  describe("Payload Validation", () => {
    it("should validate correct payload length", () => {
      const payload = "a".repeat(100);
      const result = PixFormatter.validatePayloadLength(payload);
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Payload válido");
    });

    it("should reject empty payload", () => {
      const result = PixFormatter.validatePayloadLength("");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Payload vazio");
    });

    it("should reject too long payload", () => {
      const payload = "a".repeat(600);
      const result = PixFormatter.validatePayloadLength(payload);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain("Payload muito longo");
    });

    it("should reject too short payload", () => {
      const payload = "a".repeat(30);
      const result = PixFormatter.validatePayloadLength(payload);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain("Payload muito curto");
    });
  });

  describe("TLV Parsing", () => {
    it("should parse simple TLV", () => {
      const payload = "0104test";
      const result = PixFormatter.parseTLV(payload);
      
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        tag: "01",
        length: 4,
        value: "test"
      });
    });

    it("should parse multiple TLV components", () => {
      const payload = "0104test0205hello";
      const result = PixFormatter.parseTLV(payload);
      
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        tag: "01",
        length: 4,
        value: "test"
      });
      expect(result[1]).toEqual({
        tag: "02",
        length: 5,
        value: "hello"
      });
    });

    it("should handle empty payload", () => {
      const result = PixFormatter.parseTLV("");
      expect(result).toHaveLength(0);
    });

    it("should handle malformed TLV", () => {
      const payload = "01"; // Incomplete
      const result = PixFormatter.parseTLV(payload);
      expect(result).toHaveLength(0);
    });
  });

  describe("Data Sanitization", () => {
    it("should sanitize complete PIX data", () => {
      const data = {
        keyType: "email",
        pixKey: "<EMAIL>",
        receiverName: "João da Silva",
        receiverCity: "São Paulo",
        amount: 100.50,
        reference: "REF-123",
        description: "Pagamento"
      };

      const result = PixFormatter.sanitizePixData(data);

      expect(result.keyType).toBe("email");
      expect(result.pixKey).toBe("<EMAIL>");
      expect(result.receiverName).toBe("Joao da Silva");
      expect(result.receiverCity).toBe("Sao Paulo");
      expect(result.amount).toBe(100.50);
      expect(result.reference).toBe("REF123");
      expect(result.description).toBe("Pagamento");
    });

    it("should handle null data", () => {
      const result = PixFormatter.sanitizePixData(null);
      expect(result).toEqual({});
    });

    it("should handle missing fields", () => {
      const data = {
        keyType: "email",
        pixKey: "<EMAIL>"
      };

      const result = PixFormatter.sanitizePixData(data);

      expect(result.keyType).toBe("email");
      expect(result.pixKey).toBe("<EMAIL>");
      expect(result.receiverName).toBe("");
      expect(result.receiverCity).toBe("");
      expect(result.amount).toBe(0);
      expect(result.reference).toBe("***");
      expect(result.description).toBe("");
    });

    it("should handle negative amount", () => {
      const data = {
        keyType: "email",
        pixKey: "<EMAIL>",
        amount: -100
      };

      const result = PixFormatter.sanitizePixData(data);
      expect(result.amount).toBe(0);
    });
  });

  describe("Required Fields Validation", () => {
    it("should validate complete data", () => {
      const data = {
        keyType: "email",
        pixKey: "<EMAIL>",
        receiverName: "João Silva",
        receiverCity: "São Paulo"
      };

      const result = PixFormatter.validateRequiredFields(data);
      expect(result.isValid).toBe(true);
      expect(result.missingFields).toHaveLength(0);
    });

    it("should detect missing fields", () => {
      const data = {
        keyType: "email",
        pixKey: "",
        receiverName: "João Silva"
      };

      const result = PixFormatter.validateRequiredFields(data);
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain("pixKey");
      expect(result.missingFields).toContain("receiverCity");
    });

    it("should detect whitespace-only fields", () => {
      const data = {
        keyType: "email",
        pixKey: "   ",
        receiverName: "João Silva",
        receiverCity: "São Paulo"
      };

      const result = PixFormatter.validateRequiredFields(data);
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain("pixKey");
    });
  });
});
