import CRC16Calculator from "./CRC16Calculator";

describe("CRC16Calculator", () => {
  describe("CRC16 Calculation", () => {
    it("should calculate CRC16 for simple string", () => {
      const result = CRC16Calculator.calculate("123456789");
      expect(result).toBe("29B1");
    });

    it("should calculate CRC16 for PIX payload", () => {
      const payload = "00020101021226580014br.gov.bcb.pix0136user@example.com5204000053039865802BR5913Test Receiver6009Test City62070503***6304";
      const result = CRC16Calculator.calculate(payload);
      expect(result).toMatch(/^[0-9A-F]{4}$/);
      expect(result.length).toBe(4);
    });

    it("should calculate same CRC16 for same input", () => {
      const input = "test string";
      const result1 = CRC16Calculator.calculate(input);
      const result2 = CRC16Calculator.calculate(input);
      expect(result1).toBe(result2);
    });

    it("should calculate different CRC16 for different inputs", () => {
      const result1 = CRC16Calculator.calculate("test1");
      const result2 = CRC16Calculator.calculate("test2");
      expect(result1).not.toBe(result2);
    });

    it("should throw error for empty string", () => {
      expect(() => CRC16Calculator.calculate("")).toThrow("Data cannot be empty for CRC16 calculation");
    });

    it("should handle special characters", () => {
      const result = CRC16Calculator.calculate("áéíóú@#$%");
      expect(result).toMatch(/^[0-9A-F]{4}$/);
    });

    it("should handle very long strings", () => {
      const longString = "a".repeat(1000);
      const result = CRC16Calculator.calculate(longString);
      expect(result).toMatch(/^[0-9A-F]{4}$/);
    });
  });

  describe("CRC16 Validation", () => {
    it("should validate correct CRC16", () => {
      const payload = "12345678929B1";
      const isValid = CRC16Calculator.validate(payload);
      expect(isValid).toBe(true);
    });

    it("should reject incorrect CRC16", () => {
      const payload = "123456789FFFF";
      const isValid = CRC16Calculator.validate(payload);
      expect(isValid).toBe(false);
    });

    it("should reject payload too short", () => {
      const payload = "123";
      const isValid = CRC16Calculator.validate(payload);
      expect(isValid).toBe(false);
    });

    it("should reject empty payload", () => {
      const isValid = CRC16Calculator.validate("");
      expect(isValid).toBe(false);
    });

    it("should handle case insensitive CRC16", () => {
      const payload1 = "12345678929b1";
      const payload2 = "12345678929B1";
      expect(CRC16Calculator.validate(payload1)).toBe(true);
      expect(CRC16Calculator.validate(payload2)).toBe(true);
    });
  });

  describe("CRC16 Append and Remove", () => {
    it("should append CRC16 to payload", () => {
      const payload = "123456789";
      const result = CRC16Calculator.appendCRC(payload);
      expect(result).toBe("12345678929B1");
    });

    it("should remove CRC16 from payload", () => {
      const payload = "12345678929B1";
      const result = CRC16Calculator.removeCRC(payload);
      expect(result).toBe("123456789");
    });

    it("should extract CRC16 from payload", () => {
      const payload = "12345678929B1";
      const result = CRC16Calculator.extractCRC(payload);
      expect(result).toBe("29B1");
    });

    it("should handle short payload in removeCRC", () => {
      const payload = "123";
      const result = CRC16Calculator.removeCRC(payload);
      expect(result).toBe("123");
    });

    it("should handle short payload in extractCRC", () => {
      const payload = "123";
      const result = CRC16Calculator.extractCRC(payload);
      expect(result).toBe("");
    });

    it("should throw error for empty payload in appendCRC", () => {
      expect(() => CRC16Calculator.appendCRC("")).toThrow("Payload cannot be empty");
    });
  });

  describe("CRC16 with Lookup Table", () => {
    it("should calculate same result with lookup table", () => {
      const input = "test string";
      const result1 = CRC16Calculator.calculate(input);
      const result2 = CRC16Calculator.calculateWithLookupTable(input);
      expect(result1).toBe(result2);
    });

    it("should handle empty string with lookup table", () => {
      expect(() => CRC16Calculator.calculateWithLookupTable("")).toThrow("Data cannot be empty for CRC16 calculation");
    });

    it("should calculate correct CRC16 with lookup table for known input", () => {
      const result = CRC16Calculator.calculateWithLookupTable("123456789");
      expect(result).toBe("29B1");
    });
  });

  describe("Binary CRC16 Calculation", () => {
    it("should calculate CRC16 for binary data", () => {
      const data = new Uint8Array([0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39]); // "123456789"
      const result = CRC16Calculator.calculateBinary(data);
      expect(result).toBe("29B1");
    });

    it("should throw error for empty binary data", () => {
      const data = new Uint8Array([]);
      expect(() => CRC16Calculator.calculateBinary(data)).toThrow("Data cannot be empty for CRC16 calculation");
    });

    it("should handle single byte", () => {
      const data = new Uint8Array([0x41]); // "A"
      const result = CRC16Calculator.calculateBinary(data);
      expect(result).toMatch(/^[0-9A-F]{4}$/);
    });
  });

  describe("Multiple Payload Validation", () => {
    it("should validate multiple payloads", () => {
      const payloads = [
        "12345678929B1",
        "abcdefghFFFF",
        "test123456789"
      ];
      
      const results = CRC16Calculator.validateMultiple(payloads);
      
      expect(results).toHaveLength(3);
      expect(results[0].isValid).toBe(true);
      expect(results[0].crc).toBe("29B1");
      expect(results[1].isValid).toBe(false);
      expect(results[1].crc).toBe("FFFF");
      expect(results[2].isValid).toBe(false);
    });

    it("should handle empty array", () => {
      const results = CRC16Calculator.validateMultiple([]);
      expect(results).toHaveLength(0);
    });
  });

  describe("CRC16 Utilities", () => {
    it("should compare CRC16 values case insensitive", () => {
      expect(CRC16Calculator.compare("29B1", "29b1")).toBe(true);
      expect(CRC16Calculator.compare("29B1", "29B2")).toBe(false);
      expect(CRC16Calculator.compare("", "29B1")).toBe(false);
      expect(CRC16Calculator.compare("29B1", "")).toBe(false);
    });

    it("should format CRC16 values", () => {
      expect(CRC16Calculator.format("29b1")).toBe("29B1");
      expect(CRC16Calculator.format("1")).toBe("0001");
      expect(CRC16Calculator.format("")).toBe("0000");
      expect(CRC16Calculator.format("abc")).toBe("0ABC");
    });

    it("should provide calculation details", () => {
      const details = CRC16Calculator.getCalculationDetails("123");
      
      expect(details.input).toBe("123");
      expect(details.inputLength).toBe(3);
      expect(details.inputBytes).toEqual([49, 50, 51]);
      expect(details.crc16).toMatch(/^[0-9A-F]{4}$/);
      expect(details.polynomial).toBe("1021");
      expect(details.initialValue).toBe("FFFF");
    });

    it("should throw error for empty input in getCalculationDetails", () => {
      expect(() => CRC16Calculator.getCalculationDetails("")).toThrow("Data cannot be empty for CRC16 calculation");
    });
  });

  describe("Edge Cases", () => {
    it("should handle null bytes", () => {
      const input = "test\0null";
      const result = CRC16Calculator.calculate(input);
      expect(result).toMatch(/^[0-9A-F]{4}$/);
    });

    it("should handle Unicode characters", () => {
      const input = "test🚀emoji";
      const result = CRC16Calculator.calculate(input);
      expect(result).toMatch(/^[0-9A-F]{4}$/);
    });

    it("should handle newlines and tabs", () => {
      const input = "test\n\tstring";
      const result = CRC16Calculator.calculate(input);
      expect(result).toMatch(/^[0-9A-F]{4}$/);
    });

    it("should validate complete PIX payload with CRC16", () => {
      const payloadWithoutCrc = "00020101021226580014br.gov.bcb.pix0136user@example.com5204000053039865802BR5913Test Receiver6009Test City62070503***6304";
      const completePayload = CRC16Calculator.appendCRC(payloadWithoutCrc);
      
      expect(CRC16Calculator.validate(completePayload)).toBe(true);
      expect(completePayload.length).toBe(payloadWithoutCrc.length + 4);
    });
  });
});
