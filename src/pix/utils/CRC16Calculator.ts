/**
 * CRC16 Calculator for PIX
 * Implements CRC16-CCITT algorithm as specified by Brazilian Central Bank
 * for PIX payload validation and integrity checking
 */
export default class CRC16Calculator {
  // CRC16-CCITT polynomial: 0x1021
  private static readonly POLYNOMIAL = 0x1021;
  private static readonly INITIAL_VALUE = 0xFFFF;

  /**
   * Calculates CRC16 checksum for PIX payload
   * Uses CRC16-CCITT algorithm with polynomial 0x1021
   * @param data - Input data string
   * @returns CRC16 checksum as uppercase hexadecimal string (4 characters)
   */
  static calculate(data: string): string {
    if (!data) {
      throw new Error("Data cannot be empty for CRC16 calculation");
    }

    let crc = this.INITIAL_VALUE;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= byte << 8;
      
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ this.POLYNOMIAL;
        } else {
          crc <<= 1;
        }
      }
    }

    // Ensure result is within 16-bit range and format as 4-digit hex
    return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, "0");
  }

  /**
   * Validates CRC16 checksum of a PIX payload
   * @param payload - Complete PIX payload including CRC16
   * @returns True if CRC16 is valid, false otherwise
   */
  static validate(payload: string): boolean {
    if (!payload || payload.length < 4) {
      return false;
    }

    // Extract payload without CRC16 (last 4 characters)
    const payloadWithoutCrc = payload.substring(0, payload.length - 4);
    const providedCrc = payload.substring(payload.length - 4).toUpperCase();

    // Calculate expected CRC16
    const expectedCrc = this.calculate(payloadWithoutCrc);

    return providedCrc === expectedCrc;
  }

  /**
   * Appends CRC16 checksum to a PIX payload
   * @param payloadWithoutCrc - PIX payload without CRC16
   * @returns Complete payload with CRC16 appended
   */
  static appendCRC(payloadWithoutCrc: string): string {
    if (!payloadWithoutCrc) {
      throw new Error("Payload cannot be empty");
    }

    const crc = this.calculate(payloadWithoutCrc);
    return payloadWithoutCrc + crc;
  }

  /**
   * Removes CRC16 from a PIX payload
   * @param payload - Complete PIX payload with CRC16
   * @returns Payload without CRC16
   */
  static removeCRC(payload: string): string {
    if (!payload || payload.length < 4) {
      return payload;
    }

    return payload.substring(0, payload.length - 4);
  }

  /**
   * Extracts CRC16 from a PIX payload
   * @param payload - Complete PIX payload with CRC16
   * @returns CRC16 value as uppercase hexadecimal string
   */
  static extractCRC(payload: string): string {
    if (!payload || payload.length < 4) {
      return "";
    }

    return payload.substring(payload.length - 4).toUpperCase();
  }

  /**
   * Calculates CRC16 using lookup table for better performance
   * Alternative implementation for high-throughput scenarios
   * @param data - Input data string
   * @returns CRC16 checksum as uppercase hexadecimal string
   */
  static calculateWithLookupTable(data: string): string {
    if (!data) {
      throw new Error("Data cannot be empty for CRC16 calculation");
    }

    // Generate lookup table if not already cached
    const lookupTable = this.generateLookupTable();
    
    let crc = this.INITIAL_VALUE;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      const tableIndex = ((crc >> 8) ^ byte) & 0xFF;
      crc = ((crc << 8) ^ lookupTable[tableIndex]) & 0xFFFF;
    }

    return crc.toString(16).toUpperCase().padStart(4, "0");
  }

  /**
   * Generates CRC16 lookup table for optimized calculations
   * @returns 256-element lookup table
   */
  private static generateLookupTable(): number[] {
    const table: number[] = new Array(256);

    for (let i = 0; i < 256; i++) {
      let crc = i << 8;
      
      for (let j = 0; j < 8; j++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ this.POLYNOMIAL;
        } else {
          crc <<= 1;
        }
      }
      
      table[i] = crc & 0xFFFF;
    }

    return table;
  }

  /**
   * Verifies the integrity of multiple PIX payloads
   * @param payloads - Array of PIX payloads to verify
   * @returns Array of validation results
   */
  static validateMultiple(payloads: string[]): Array<{ payload: string; isValid: boolean; crc: string }> {
    return payloads.map(payload => ({
      payload,
      isValid: this.validate(payload),
      crc: this.extractCRC(payload)
    }));
  }

  /**
   * Calculates CRC16 for binary data
   * @param data - Binary data as Uint8Array
   * @returns CRC16 checksum as uppercase hexadecimal string
   */
  static calculateBinary(data: Uint8Array): string {
    if (!data || data.length === 0) {
      throw new Error("Data cannot be empty for CRC16 calculation");
    }

    let crc = this.INITIAL_VALUE;

    for (const byte of data) {
      crc ^= byte << 8;
      
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ this.POLYNOMIAL;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, "0");
  }

  /**
   * Provides detailed CRC16 calculation information for debugging
   * @param data - Input data string
   * @returns Detailed calculation information
   */
  static getCalculationDetails(data: string): {
    input: string;
    inputLength: number;
    inputBytes: number[];
    crc16: string;
    polynomial: string;
    initialValue: string;
  } {
    if (!data) {
      throw new Error("Data cannot be empty for CRC16 calculation");
    }

    const dataBytes = new TextEncoder().encode(data);
    const crc16 = this.calculate(data);

    return {
      input: data,
      inputLength: data.length,
      inputBytes: Array.from(dataBytes),
      crc16,
      polynomial: this.POLYNOMIAL.toString(16).toUpperCase(),
      initialValue: this.INITIAL_VALUE.toString(16).toUpperCase()
    };
  }

  /**
   * Compares two CRC16 values
   * @param crc1 - First CRC16 value
   * @param crc2 - Second CRC16 value
   * @returns True if CRC16 values match (case-insensitive)
   */
  static compare(crc1: string, crc2: string): boolean {
    if (!crc1 || !crc2) {
      return false;
    }

    return crc1.toUpperCase() === crc2.toUpperCase();
  }

  /**
   * Formats CRC16 value for display
   * @param crc - CRC16 value
   * @returns Formatted CRC16 string
   */
  static format(crc: string): string {
    if (!crc) {
      return "0000";
    }

    return crc.toUpperCase().padStart(4, "0");
  }
}
