/**
 * PIX Formatter Utilities
 * Provides text formatting and normalization functions
 * according to Brazilian Central Bank PIX specifications
 */
export default class PixFormatter {
  /**
   * Formats text according to PIX specifications
   * Removes accents, special characters, and normalizes text
   * @param text - Text to format
   * @returns Formatted text compliant with PIX standards
   */
  static formatText(text: string): string {
    if (!text) return "";

    return text
      .normalize("NFD") // Normalize to decomposed form
      .replace(/[\u0300-\u036f]/g, "") // Remove diacritical marks (accents)
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, "") // Keep only allowed characters
      .trim();
  }

  /**
   * Creates a PIX payload tag-length-value (TLV) formatted string
   * @param tag - Two-digit tag identifier
   * @param value - Value to encode
   * @returns TLV formatted string (tag + length + value)
   */
  static getValue(tag: string, value: string): string {
    if (!tag || !value) return "";
    
    // Ensure tag is exactly 2 digits
    const formattedTag = tag.padStart(2, "0");
    
    // Calculate length and pad to 2 digits
    const length = value.length.toString().padStart(2, "0");
    
    return `${formattedTag}${length}${value}`;
  }

  /**
   * Formats monetary amount for PIX payload
   * @param amount - Numeric amount
   * @returns Formatted amount string with 2 decimal places
   */
  static formatAmount(amount: number): string {
    if (amount <= 0) return "";
    
    return amount.toFixed(2);
  }

  /**
   * Normalizes PIX key based on its type
   * @param keyType - Type of PIX key
   * @param key - Raw key value
   * @returns Normalized key string
   */
  static normalizePixKey(keyType: string, key: string): string {
    if (!key) return "";

    switch (keyType) {
      case "cpf":
        // Remove all non-digit characters
        return key.replace(/\D/g, "");
      
      case "phone":
        // Remove all non-digit characters and ensure international format
        let cleanPhone = key.replace(/\D/g, "");
        
        // Add country code if not present
        if (!cleanPhone.startsWith("55")) {
          cleanPhone = "55" + cleanPhone;
        }
        
        // Add + prefix if not present
        if (!cleanPhone.startsWith("+")) {
          cleanPhone = "+" + cleanPhone;
        }
        
        return cleanPhone;
      
      case "email":
        // Convert to lowercase and trim
        return key.toLowerCase().trim();
      
      case "random":
        // Keep as is, just trim
        return key.trim();
      
      default:
        return key.trim();
    }
  }

  /**
   * Validates and formats receiver name for PIX payload
   * @param name - Receiver name
   * @param maxLength - Maximum allowed length (default: 25)
   * @returns Formatted name
   */
  static formatReceiverName(name: string, maxLength: number = 25): string {
    if (!name) return "";
    
    const formatted = this.formatText(name);
    return formatted.substring(0, maxLength);
  }

  /**
   * Validates and formats receiver city for PIX payload
   * @param city - Receiver city
   * @param maxLength - Maximum allowed length (default: 15)
   * @returns Formatted city
   */
  static formatReceiverCity(city: string, maxLength: number = 15): string {
    if (!city) return "";
    
    const formatted = this.formatText(city);
    return formatted.substring(0, maxLength);
  }

  /**
   * Formats transaction reference/identifier
   * @param reference - Transaction reference
   * @param maxLength - Maximum allowed length (default: 25)
   * @returns Formatted reference
   */
  static formatReference(reference: string, maxLength: number = 25): string {
    if (!reference) return "***"; // Default reference as per PIX specification
    
    // Allow only alphanumeric characters for reference
    const cleaned = reference.replace(/[^A-Za-z0-9]/g, "");
    return cleaned.substring(0, maxLength) || "***";
  }

  /**
   * Formats transaction description
   * @param description - Transaction description
   * @param maxLength - Maximum allowed length (default: 50)
   * @returns Formatted description
   */
  static formatDescription(description: string, maxLength: number = 50): string {
    if (!description) return "";
    
    const formatted = this.formatText(description);
    return formatted.substring(0, maxLength);
  }

  /**
   * Validates PIX payload length constraints
   * @param payload - PIX payload string
   * @returns Validation result
   */
  static validatePayloadLength(payload: string): { isValid: boolean; message: string } {
    if (!payload) {
      return { isValid: false, message: "Payload vazio" };
    }

    // PIX payload should not exceed 512 characters
    if (payload.length > 512) {
      return { 
        isValid: false, 
        message: `Payload muito longo: ${payload.length} caracteres (máximo: 512)` 
      };
    }

    // PIX payload should have minimum length
    if (payload.length < 50) {
      return { 
        isValid: false, 
        message: `Payload muito curto: ${payload.length} caracteres (mínimo: 50)` 
      };
    }

    return { isValid: true, message: "Payload válido" };
  }

  /**
   * Extracts and validates TLV components from a PIX payload
   * @param payload - PIX payload string
   * @returns Array of TLV components
   */
  static parseTLV(payload: string): Array<{ tag: string; length: number; value: string }> {
    const components: Array<{ tag: string; length: number; value: string }> = [];
    let position = 0;

    while (position < payload.length) {
      // Extract tag (2 digits)
      if (position + 2 > payload.length) break;
      const tag = payload.substring(position, position + 2);
      position += 2;

      // Extract length (2 digits)
      if (position + 2 > payload.length) break;
      const lengthStr = payload.substring(position, position + 2);
      const length = parseInt(lengthStr, 10);
      position += 2;

      // Extract value
      if (position + length > payload.length) break;
      const value = payload.substring(position, position + length);
      position += length;

      components.push({ tag, length, value });
    }

    return components;
  }

  /**
   * Formats a PIX payload for display purposes
   * @param payload - PIX payload string
   * @returns Formatted payload with line breaks and indentation
   */
  static formatPayloadForDisplay(payload: string): string {
    const components = this.parseTLV(payload);
    const lines: string[] = [];

    components.forEach(({ tag, length, value }) => {
      const tagDescription = this.getTagDescription(tag);
      lines.push(`${tag} (${length.toString().padStart(2, '0')}): ${value} ${tagDescription ? `// ${tagDescription}` : ""}`);
    });

    return lines.join("\n");
  }

  /**
   * Gets human-readable description for PIX payload tags
   * @param tag - PIX tag identifier
   * @returns Tag description
   */
  private static getTagDescription(tag: string): string {
    const descriptions: { [key: string]: string } = {
      "00": "Payload Format Indicator",
      "01": "Point of Initiation Method",
      "26": "Merchant Account Information",
      "52": "Merchant Category Code",
      "53": "Transaction Currency",
      "54": "Transaction Amount",
      "58": "Country Code",
      "59": "Merchant Name",
      "60": "Merchant City",
      "62": "Additional Data Field Template",
      "63": "CRC16"
    };

    return descriptions[tag] || "";
  }

  /**
   * Sanitizes input data for PIX payload generation
   * @param data - Raw input data
   * @returns Sanitized data object
   */
  static sanitizePixData(data: any): any {
    if (!data || typeof data !== "object") {
      return {};
    }

    return {
      keyType: data.keyType || "",
      pixKey: this.normalizePixKey(data.keyType, data.pixKey || ""),
      receiverName: this.formatReceiverName(data.receiverName || ""),
      receiverCity: this.formatReceiverCity(data.receiverCity || ""),
      amount: typeof data.amount === "number" && data.amount > 0 ? data.amount : 0,
      reference: this.formatReference(data.reference || ""),
      description: this.formatDescription(data.description || "")
    };
  }

  /**
   * Validates required fields for PIX payload generation
   * @param data - PIX data object
   * @returns Validation result
   */
  static validateRequiredFields(data: any): { isValid: boolean; missingFields: string[] } {
    const requiredFields = ["keyType", "pixKey", "receiverName", "receiverCity"];
    const missingFields: string[] = [];

    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === "string" && !data[field].trim())) {
        missingFields.push(field);
      }
    });

    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  }
}
