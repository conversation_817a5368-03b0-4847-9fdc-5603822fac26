/**
 * Simple PIX Module Test
 * Basic functionality test without Jest dependencies
 */

const path = require('path');

// Test helper function
function assert(condition, message) {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`);
  }
  console.log(`✅ ${message}`);
}

function runTests() {
  console.log('🧪 Running PIX Module Tests...\n');

  try {
    // Test 1: Module imports
    console.log('Test 1: Module Imports');
    const PixKeyValidator = require('./validators/PixKeyValidator').default;
    const CRC16Calculator = require('./utils/CRC16Calculator').default;
    const PixFormatter = require('./utils/PixFormatter').default;
    const PixBRCodeGenerator = require('./core/PixBRCodeGenerator').default;
    
    assert(typeof PixKeyValidator === 'function', 'PixKeyValidator imported successfully');
    assert(typeof CRC16Calculator === 'function', 'CRC16Calculator imported successfully');
    assert(typeof PixFormatter === 'function', 'PixFormatter imported successfully');
    assert(typeof PixBRCodeGenerator === 'function', 'PixBRCodeGenerator imported successfully');

    // Test 2: CRC16 Calculation
    console.log('\nTest 2: CRC16 Calculation');
    const crc1 = CRC16Calculator.calculate('123456789');
    assert(crc1 === '29B1', `CRC16 calculation correct: ${crc1}`);
    
    const isValidCRC = CRC16Calculator.validate('12345678929B1');
    assert(isValidCRC === true, 'CRC16 validation works');

    // Test 3: PIX Key Validation
    console.log('\nTest 3: PIX Key Validation');
    
    // Valid CPF
    const cpfResult = PixKeyValidator.validate('cpf', '11144477735');
    assert(cpfResult.isValid === true, 'Valid CPF accepted');
    
    // Invalid CPF
    const invalidCpfResult = PixKeyValidator.validate('cpf', '12345678901');
    assert(invalidCpfResult.isValid === false, 'Invalid CPF rejected');
    
    // Valid email
    const emailResult = PixKeyValidator.validate('email', '<EMAIL>');
    assert(emailResult.isValid === true, 'Valid email accepted');
    
    // Invalid email
    const invalidEmailResult = PixKeyValidator.validate('email', 'invalid-email');
    assert(invalidEmailResult.isValid === false, 'Invalid email rejected');

    // Test 4: Text Formatting
    console.log('\nTest 4: Text Formatting');
    const formatted = PixFormatter.formatText('João da Silva');
    assert(formatted === 'Joao da Silva', `Text formatting works: ${formatted}`);
    
    const tlv = PixFormatter.getValue('01', 'test');
    assert(tlv === '0104test', `TLV formatting works: ${tlv}`);

    // Test 5: PIX BR Code Generation
    console.log('\nTest 5: PIX BR Code Generation');
    const pixData = {
      keyType: 'email',
      pixKey: '<EMAIL>',
      receiverName: 'João Silva',
      receiverCity: 'São Paulo',
      amount: 100.50,
      reference: 'REF123',
      description: 'Test payment'
    };
    
    const brCodeResult = PixBRCodeGenerator.generate(pixData);
    assert(brCodeResult.isValid === true, 'BR Code generation successful');
    assert(typeof brCodeResult.brCode === 'string', 'BR Code is a string');
    assert(brCodeResult.brCode.length > 50, `BR Code has reasonable length: ${brCodeResult.brCode.length}`);
    
    // Validate the generated BR Code has valid CRC16
    const generatedCRC = CRC16Calculator.validate(brCodeResult.brCode);
    assert(generatedCRC === true, 'Generated BR Code has valid CRC16');

    // Test 6: BR Code Parsing
    console.log('\nTest 6: BR Code Parsing');
    const parsedData = PixBRCodeGenerator.parse(brCodeResult.brCode);
    assert(parsedData !== null, 'BR Code parsing successful');
    assert(parsedData.pixKey === '<EMAIL>', `PIX key parsed correctly: ${parsedData.pixKey}`);
    assert(parsedData.receiverName === 'Joao Silva', `Receiver name parsed correctly: ${parsedData.receiverName}`);
    assert(parsedData.amount === 100.50, `Amount parsed correctly: ${parsedData.amount}`);

    // Test 7: Edge Cases
    console.log('\nTest 7: Edge Cases');
    
    // Empty data
    const emptyResult = PixBRCodeGenerator.generate(null);
    assert(emptyResult.isValid === false, 'Empty data rejected');
    
    // Missing required fields
    const incompleteData = { keyType: 'email', pixKey: '<EMAIL>' };
    const incompleteResult = PixBRCodeGenerator.generate(incompleteData);
    assert(incompleteResult.isValid === false, 'Incomplete data rejected');
    
    // Invalid PIX key
    const invalidKeyData = {
      keyType: 'cpf',
      pixKey: 'invalid-cpf',
      receiverName: 'Test',
      receiverCity: 'Test'
    };
    const invalidKeyResult = PixBRCodeGenerator.generate(invalidKeyData);
    assert(invalidKeyResult.isValid === false, 'Invalid PIX key rejected');

    console.log('\n🎉 All tests passed! PIX module is working correctly.');
    return true;

  } catch (error) {
    console.error(`\n❌ Test failed: ${error.message}`);
    console.error(error.stack);
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runTests };
