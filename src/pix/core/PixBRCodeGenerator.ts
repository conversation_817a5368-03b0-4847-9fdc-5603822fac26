import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PixPayloadOptions } from "../../types";
import Pix<PERSON>eyValidator from "../validators/PixKeyValidator";
import PixFormatter from "../utils/PixFormatter";
import CRC<PERSON><PERSON>alculator from "../utils/CRC16Calculator";

/**
 * PIX BR Code Generator
 * Main class responsible for generating PIX payment codes (BR Code)
 * according to Brazilian Central Bank specifications
 */
export default class PixBRCodeGenerator {
  // PIX Constants as per Central Bank specification
  private static readonly PAYLOAD_FORMAT_INDICATOR = "01";
  private static readonly POINT_OF_INITIATION_METHOD_STATIC = "11";

  private static readonly PIX_IDENTIFIER = "br.gov.bcb.pix";
  private static readonly MERCHANT_CATEGORY_CODE_DEFAULT = "0000";
  private static readonly TRANSACTION_CURRENCY_BRL = "986";
  private static readonly COUNTRY_CODE_BRAZIL = "BR";
  private static readonly CRC_PLACEHOLDER = "6304";

  // PIX Tags
  private static readonly TAGS = {
    PAYLOAD_FORMAT_INDICATOR: "00",
    POINT_OF_INITIATION_METHOD: "01",
    MERCHANT_ACCOUNT_INFO: "26",
    MERCHANT_CATEGORY_CODE: "52",
    TRANSACTION_CURRENCY: "53",
    TRANSACTION_AMOUNT: "54",
    COUNTRY_CODE: "58",
    MERCHANT_NAME: "59",
    MERCHANT_CITY: "60",
    ADDITIONAL_DATA: "62",
    CRC16: "63"
  };

  // Merchant Account Information Sub-tags
  private static readonly MERCHANT_ACCOUNT_SUBTAGS = {
    GUI: "00",
    PIX_KEY: "01",
    DESCRIPTION: "02"
  };

  // Additional Data Sub-tags
  private static readonly ADDITIONAL_DATA_SUBTAGS = {
    REFERENCE: "05"
  };

  /**
   * Generates a PIX BR Code from the provided data
   * @param pixData - PIX transaction data
   * @param options - Optional payload generation options
   * @returns PIX BR Code result with validation status
   */
  static generate(pixData: PixData, options: PixPayloadOptions = {}): PixBRCodeResult {
    try {
      // Validate input data
      const validationResult = this.validateInput(pixData);
      if (!validationResult.isValid) {
        return {
          brCode: "",
          isValid: false,
          error: validationResult.error
        };
      }

      // Sanitize and format input data
      const sanitizedData = this.sanitizePixData(pixData);

      // Validate PIX key
      const keyValidation = PixKeyValidator.validate(
        sanitizedData.keyType,
        sanitizedData.pixKey
      );
      
      if (!keyValidation.isValid) {
        return {
          brCode: "",
          isValid: false,
          error: `Chave PIX inválida: ${keyValidation.message}`
        };
      }

      // Generate BR Code
      const brCode = this.buildBRCode(sanitizedData, options);

      // Validate generated payload
      const payloadValidation = PixFormatter.validatePayloadLength(brCode);
      if (!payloadValidation.isValid) {
        return {
          brCode: "",
          isValid: false,
          error: payloadValidation.message
        };
      }

      // Validate CRC16
      const isCrcValid = CRC16Calculator.validate(brCode);
      if (!isCrcValid) {
        return {
          brCode: "",
          isValid: false,
          error: "CRC16 inválido no payload gerado"
        };
      }

      return {
        brCode,
        isValid: true
      };

    } catch (error) {
      return {
        brCode: "",
        isValid: false,
        error: `Erro interno: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * Builds the complete BR Code payload
   * @param data - Sanitized PIX data
   * @param options - Payload options
   * @returns Complete BR Code string
   */
  private static buildBRCode(data: PixData, options: PixPayloadOptions): string {
    let payload = "";

    // 00 - Payload Format Indicator
    payload += PixFormatter.getValue(
      this.TAGS.PAYLOAD_FORMAT_INDICATOR,
      this.PAYLOAD_FORMAT_INDICATOR
    );

    // 01 - Point of Initiation Method
    const initiationMethod = data.amount && data.amount > 0 
      ? this.POINT_OF_INITIATION_METHOD_STATIC 
      : this.POINT_OF_INITIATION_METHOD_STATIC;
    
    payload += PixFormatter.getValue(
      this.TAGS.POINT_OF_INITIATION_METHOD,
      initiationMethod
    );

    // 26 - Merchant Account Information
    payload += this.buildMerchantAccountInfo(data);

    // 52 - Merchant Category Code
    const merchantCategoryCode = options.merchantCategoryCode || this.MERCHANT_CATEGORY_CODE_DEFAULT;
    payload += PixFormatter.getValue(
      this.TAGS.MERCHANT_CATEGORY_CODE,
      merchantCategoryCode
    );

    // 53 - Transaction Currency
    const transactionCurrency = options.transactionCurrency || this.TRANSACTION_CURRENCY_BRL;
    payload += PixFormatter.getValue(
      this.TAGS.TRANSACTION_CURRENCY,
      transactionCurrency
    );

    // 54 - Transaction Amount (optional)
    if (data.amount && data.amount > 0) {
      payload += PixFormatter.getValue(
        this.TAGS.TRANSACTION_AMOUNT,
        PixFormatter.formatAmount(data.amount)
      );
    }

    // 58 - Country Code
    const countryCode = options.countryCode || this.COUNTRY_CODE_BRAZIL;
    payload += PixFormatter.getValue(
      this.TAGS.COUNTRY_CODE,
      countryCode
    );

    // 59 - Merchant Name
    payload += PixFormatter.getValue(
      this.TAGS.MERCHANT_NAME,
      data.receiverName
    );

    // 60 - Merchant City
    payload += PixFormatter.getValue(
      this.TAGS.MERCHANT_CITY,
      data.receiverCity
    );

    // 62 - Additional Data Field Template
    payload += this.buildAdditionalDataField(data);

    // 63 - CRC16 (placeholder + calculated value)
    payload += this.CRC_PLACEHOLDER;
    
    // Calculate and append CRC16
    const finalPayload = CRC16Calculator.appendCRC(payload);

    return finalPayload;
  }

  /**
   * Builds the Merchant Account Information field (Tag 26)
   * @param data - PIX data
   * @returns Formatted merchant account information
   */
  private static buildMerchantAccountInfo(data: PixData): string {
    let accountInfo = "";

    // 00 - GUI (Globally Unique Identifier)
    accountInfo += PixFormatter.getValue(
      this.MERCHANT_ACCOUNT_SUBTAGS.GUI,
      this.PIX_IDENTIFIER
    );

    // 01 - PIX Key
    accountInfo += PixFormatter.getValue(
      this.MERCHANT_ACCOUNT_SUBTAGS.PIX_KEY,
      data.pixKey
    );

    // 02 - Description (optional)
    if (data.description) {
      accountInfo += PixFormatter.getValue(
        this.MERCHANT_ACCOUNT_SUBTAGS.DESCRIPTION,
        data.description
      );
    }

    // Wrap in Tag 26
    return PixFormatter.getValue(this.TAGS.MERCHANT_ACCOUNT_INFO, accountInfo);
  }

  /**
   * Builds the Additional Data Field Template (Tag 62)
   * @param data - PIX data
   * @returns Formatted additional data field
   */
  private static buildAdditionalDataField(data: PixData): string {
    let additionalData = "";

    // 05 - Reference/Transaction ID
    const reference = data.reference || "***";
    additionalData += PixFormatter.getValue(
      this.ADDITIONAL_DATA_SUBTAGS.REFERENCE,
      reference
    );

    // Wrap in Tag 62
    return PixFormatter.getValue(this.TAGS.ADDITIONAL_DATA, additionalData);
  }

  /**
   * Validates input data for BR Code generation
   * @param data - PIX data to validate
   * @returns Validation result
   */
  private static validateInput(data: PixData): { isValid: boolean; error?: string } {
    if (!data) {
      return { isValid: false, error: "Dados PIX não fornecidos" };
    }

    // Check required fields
    const validation = this.validateRequiredFields(data);
    if (!validation.isValid) {
      return {
        isValid: false,
        error: `Campos obrigatórios ausentes: ${validation.missingFields.join(", ")}`
      };
    }

    // Validate amount if provided
    if (data.amount !== undefined && data.amount < 0) {
      return { isValid: false, error: "Valor da transação não pode ser negativo" };
    }

    // Validate amount precision (max 2 decimal places)
    if (data.amount && data.amount > 0) {
      const amountStr = data.amount.toString();
      const decimalPart = amountStr.split(".")[1];
      if (decimalPart && decimalPart.length > 2) {
        return { isValid: false, error: "Valor da transação deve ter no máximo 2 casas decimais" };
      }
    }

    return { isValid: true };
  }

  /**
   * Sanitizes input data for PIX payload generation
   * @param data - Raw input data
   * @returns Sanitized data object
   */
  private static sanitizePixData(data: PixData): PixData {
    return {
      keyType: data.keyType || "email",
      pixKey: PixFormatter.normalizePixKey(data.keyType, data.pixKey || ""),
      receiverName: PixFormatter.formatReceiverName(data.receiverName || ""),
      receiverCity: PixFormatter.formatReceiverCity(data.receiverCity || ""),
      amount: typeof data.amount === "number" && data.amount > 0 ? data.amount : 0,
      reference: PixFormatter.formatReference(data.reference || ""),
      description: PixFormatter.formatDescription(data.description || "")
    };
  }

  /**
   * Validates required fields for PIX payload generation
   * @param data - PIX data object
   * @returns Validation result
   */
  private static validateRequiredFields(data: PixData): { isValid: boolean; missingFields: string[] } {
    const requiredFields = ["keyType", "pixKey", "receiverName", "receiverCity"];
    const missingFields: string[] = [];

    requiredFields.forEach(field => {
      const value = (data as any)[field];
      if (!value || (typeof value === "string" && !value.trim())) {
        missingFields.push(field);
      }
    });

    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  }

  /**
   * Parses an existing BR Code and extracts PIX data
   * @param brCode - BR Code string to parse
   * @returns Parsed PIX data or null if invalid
   */
  static parse(brCode: string): PixData | null {
    try {
      if (!brCode || !CRC16Calculator.validate(brCode)) {
        return null;
      }

      const components = PixFormatter.parseTLV(brCode);
      const pixData: Partial<PixData> = {};

      for (const component of components) {
        switch (component.tag) {
          case this.TAGS.TRANSACTION_AMOUNT:
            pixData.amount = parseFloat(component.value);
            break;
          
          case this.TAGS.MERCHANT_NAME:
            pixData.receiverName = component.value;
            break;
          
          case this.TAGS.MERCHANT_CITY:
            pixData.receiverCity = component.value;
            break;
          
          case this.TAGS.MERCHANT_ACCOUNT_INFO:
            const accountInfo = this.parseMerchantAccountInfo(component.value);
            if (accountInfo) {
              pixData.pixKey = accountInfo.pixKey;
              pixData.description = accountInfo.description;
              pixData.keyType = this.detectKeyType(accountInfo.pixKey);
            }
            break;
          
          case this.TAGS.ADDITIONAL_DATA:
            const additionalData = this.parseAdditionalDataField(component.value);
            if (additionalData) {
              pixData.reference = additionalData.reference;
            }
            break;
        }
      }

      // Validate that we have minimum required data
      if (!pixData.pixKey || !pixData.receiverName || !pixData.receiverCity) {
        return null;
      }

      return pixData as PixData;

    } catch (error) {
      return null;
    }
  }

  /**
   * Parses merchant account information from Tag 26
   * @param accountInfoValue - Raw account info value
   * @returns Parsed account information
   */
  private static parseMerchantAccountInfo(accountInfoValue: string): { pixKey: string; description?: string } | null {
    try {
      const components = PixFormatter.parseTLV(accountInfoValue);
      let pixKey = "";
      let description = "";

      for (const component of components) {
        switch (component.tag) {
          case this.MERCHANT_ACCOUNT_SUBTAGS.PIX_KEY:
            pixKey = component.value;
            break;
          case this.MERCHANT_ACCOUNT_SUBTAGS.DESCRIPTION:
            description = component.value;
            break;
        }
      }

      return pixKey ? { pixKey, description: description || undefined } : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Parses additional data field from Tag 62
   * @param additionalDataValue - Raw additional data value
   * @returns Parsed additional data
   */
  private static parseAdditionalDataField(additionalDataValue: string): { reference: string } | null {
    try {
      const components = PixFormatter.parseTLV(additionalDataValue);
      
      for (const component of components) {
        if (component.tag === this.ADDITIONAL_DATA_SUBTAGS.REFERENCE) {
          return { reference: component.value };
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Detects PIX key type based on key format
   * @param pixKey - PIX key string
   * @returns Detected key type
   */
  private static detectKeyType(pixKey: string): PixData["keyType"] {
    if (!pixKey) return "random";

    // CPF pattern (11 digits)
    if (/^\d{11}$/.test(pixKey)) {
      return "cpf";
    }

    // Phone pattern (starts with + and has digits)
    if (/^\+\d{12,13}$/.test(pixKey)) {
      return "phone";
    }

    // Email pattern
    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(pixKey)) {
      return "email";
    }

    // Default to random key
    return "random";
  }
}
