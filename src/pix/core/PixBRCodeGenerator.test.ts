import PixBRCodeGenerator from "./PixBRCodeGenerator";
import { PixData } from "../../types";

describe("PixBRCodeGenerator", () => {
  const validPixData: PixData = {
    keyType: "email",
    pixKey: "<EMAIL>",
    receiverName: "<PERSON>",
    receiverCity: "São Paulo",
    amount: 100.50,
    reference: "REF123",
    description: "Pagamento de serviços"
  };

  describe("BR Code Generation", () => {
    it("should generate valid BR Code for complete data", () => {
      const result = PixBRCodeGenerator.generate(validPixData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toBeDefined();
      expect(result.brCode.length).toBeGreaterThan(50);
      expect(result.error).toBeUndefined();
    });

    it("should generate BR Code without amount", () => {
      const dataWithoutAmount = { ...validPixData, amount: 0 };
      const result = PixBRCodeGenerator.generate(dataWithoutAmount);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toBeDefined();
      expect(result.brCode).not.toContain("54"); // Amount tag should not be present
    });

    it("should generate BR Code without optional fields", () => {
      const minimalData: PixData = {
        keyType: "email",
        pixKey: "<EMAIL>",
        receiverName: "João Silva",
        receiverCity: "São Paulo"
      };
      
      const result = PixBRCodeGenerator.generate(minimalData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toBeDefined();
    });

    it("should include all required PIX tags", () => {
      const result = PixBRCodeGenerator.generate(validPixData);
      
      expect(result.isValid).toBe(true);
      
      const brCode = result.brCode;
      expect(brCode).toContain("00"); // Payload Format Indicator
      expect(brCode).toContain("01"); // Point of Initiation Method
      expect(brCode).toContain("26"); // Merchant Account Information
      expect(brCode).toContain("52"); // Merchant Category Code
      expect(brCode).toContain("53"); // Transaction Currency
      expect(brCode).toContain("58"); // Country Code
      expect(brCode).toContain("59"); // Merchant Name
      expect(brCode).toContain("60"); // Merchant City
      expect(brCode).toContain("62"); // Additional Data
      expect(brCode).toContain("63"); // CRC16
    });

    it("should include amount tag when amount is provided", () => {
      const result = PixBRCodeGenerator.generate(validPixData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("54"); // Transaction Amount tag
      expect(result.brCode).toContain("100.50");
    });

    it("should handle CPF key type", () => {
      const cpfData: PixData = {
        ...validPixData,
        keyType: "cpf",
        pixKey: "***********"
      };
      
      const result = PixBRCodeGenerator.generate(cpfData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("***********");
    });

    it("should handle phone key type", () => {
      const phoneData: PixData = {
        ...validPixData,
        keyType: "phone",
        pixKey: "+5511987654321"
      };
      
      const result = PixBRCodeGenerator.generate(phoneData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("+5511987654321");
    });

    it("should handle random key type", () => {
      const randomData: PixData = {
        ...validPixData,
        keyType: "random",
        pixKey: "550e8400-e29b-41d4-a716-446655440000"
      };
      
      const result = PixBRCodeGenerator.generate(randomData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("550e8400-e29b-41d4-a716-446655440000");
    });
  });

  describe("BR Code Validation", () => {
    it("should reject null data", () => {
      const result = PixBRCodeGenerator.generate(null as any);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe("Dados PIX não fornecidos");
    });

    it("should reject missing required fields", () => {
      const incompleteData = {
        keyType: "email",
        pixKey: "<EMAIL>"
        // Missing receiverName and receiverCity
      } as PixData;
      
      const result = PixBRCodeGenerator.generate(incompleteData);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Campos obrigatórios ausentes");
    });

    it("should reject invalid PIX key", () => {
      const invalidData: PixData = {
        ...validPixData,
        keyType: "cpf",
        pixKey: "invalid-cpf"
      };
      
      const result = PixBRCodeGenerator.generate(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Chave PIX inválida");
    });

    it("should reject negative amount", () => {
      const negativeAmountData: PixData = {
        ...validPixData,
        amount: -100
      };
      
      const result = PixBRCodeGenerator.generate(negativeAmountData);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe("Valor da transação não pode ser negativo");
    });

    it("should reject amount with too many decimal places", () => {
      const preciseAmountData: PixData = {
        ...validPixData,
        amount: 100.123
      };
      
      const result = PixBRCodeGenerator.generate(preciseAmountData);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe("Valor da transação deve ter no máximo 2 casas decimais");
    });
  });

  describe("BR Code Parsing", () => {
    it("should parse valid BR Code", () => {
      const generateResult = PixBRCodeGenerator.generate(validPixData);
      expect(generateResult.isValid).toBe(true);
      
      const parsedData = PixBRCodeGenerator.parse(generateResult.brCode);
      
      expect(parsedData).not.toBeNull();
      expect(parsedData!.pixKey).toBe("<EMAIL>");
      expect(parsedData!.receiverName).toBe("Joao Silva"); // Formatted without accents
      expect(parsedData!.receiverCity).toBe("Sao Paulo"); // Formatted without accents
      expect(parsedData!.amount).toBe(100.50);
    });

    it("should return null for invalid BR Code", () => {
      const parsedData = PixBRCodeGenerator.parse("invalid-br-code");
      expect(parsedData).toBeNull();
    });

    it("should return null for empty BR Code", () => {
      const parsedData = PixBRCodeGenerator.parse("");
      expect(parsedData).toBeNull();
    });

    it("should detect correct key type from parsed data", () => {
      const testCases = [
        { keyType: "cpf" as const, pixKey: "***********" },
        { keyType: "phone" as const, pixKey: "+5511987654321" },
        { keyType: "email" as const, pixKey: "<EMAIL>" },
        { keyType: "random" as const, pixKey: "550e8400-e29b-41d4-a716-446655440000" }
      ];

      testCases.forEach(({ keyType, pixKey }) => {
        const testData: PixData = {
          ...validPixData,
          keyType,
          pixKey
        };

        const generateResult = PixBRCodeGenerator.generate(testData);
        expect(generateResult.isValid).toBe(true);

        const parsedData = PixBRCodeGenerator.parse(generateResult.brCode);
        expect(parsedData).not.toBeNull();
        expect(parsedData!.keyType).toBe(keyType);
        expect(parsedData!.pixKey).toBe(pixKey);
      });
    });
  });

  describe("Custom Options", () => {
    it("should use custom merchant category code", () => {
      const options = { merchantCategoryCode: "1234" };
      const result = PixBRCodeGenerator.generate(validPixData, options);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("52041234"); // Tag 52, length 04, value 1234
    });

    it("should use custom transaction currency", () => {
      const options = { transactionCurrency: "840" }; // USD
      const result = PixBRCodeGenerator.generate(validPixData, options);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("5303840"); // Tag 53, length 03, value 840
    });

    it("should use custom country code", () => {
      const options = { countryCode: "US" };
      const result = PixBRCodeGenerator.generate(validPixData, options);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("5802US"); // Tag 58, length 02, value US
    });

    it("should use default values when options not provided", () => {
      const result = PixBRCodeGenerator.generate(validPixData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("52040000"); // Default merchant category code
      expect(result.brCode).toContain("5303986"); // Default currency (BRL)
      expect(result.brCode).toContain("5802BR"); // Default country code
    });
  });

  describe("Edge Cases", () => {
    it("should handle very long receiver name", () => {
      const longNameData: PixData = {
        ...validPixData,
        receiverName: "a".repeat(50) // Will be truncated to 25
      };
      
      const result = PixBRCodeGenerator.generate(longNameData);
      
      expect(result.isValid).toBe(true);
      // Should be truncated and formatted
      expect(result.brCode).toContain("a".repeat(25));
    });

    it("should handle special characters in description", () => {
      const specialCharData: PixData = {
        ...validPixData,
        description: "Pagamento & Cobrança <test>"
      };
      
      const result = PixBRCodeGenerator.generate(specialCharData);
      
      expect(result.isValid).toBe(true);
      // Special characters should be removed
      expect(result.brCode).not.toContain("&");
      expect(result.brCode).not.toContain("<");
      expect(result.brCode).not.toContain(">");
    });

    it("should handle zero amount correctly", () => {
      const zeroAmountData: PixData = {
        ...validPixData,
        amount: 0
      };
      
      const result = PixBRCodeGenerator.generate(zeroAmountData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).not.toContain("54"); // Amount tag should not be present
    });

    it("should handle undefined optional fields", () => {
      const minimalData: PixData = {
        keyType: "email",
        pixKey: "<EMAIL>",
        receiverName: "João Silva",
        receiverCity: "São Paulo",
        amount: undefined,
        reference: undefined,
        description: undefined
      };
      
      const result = PixBRCodeGenerator.generate(minimalData);
      
      expect(result.isValid).toBe(true);
      expect(result.brCode).toContain("62070503***"); // Default reference
    });
  });

  describe("CRC16 Validation", () => {
    it("should generate BR Code with valid CRC16", () => {
      const result = PixBRCodeGenerator.generate(validPixData);
      
      expect(result.isValid).toBe(true);
      
      // Extract CRC16 (last 4 characters)
      const crc = result.brCode.slice(-4);
      expect(crc).toMatch(/^[0-9A-F]{4}$/);
      
      // Validate complete payload
      const payloadWithoutCrc = result.brCode.slice(0, -4);
      expect(result.brCode).toBe(payloadWithoutCrc + crc);
    });

    it("should reject BR Code with invalid CRC16 during parsing", () => {
      const validResult = PixBRCodeGenerator.generate(validPixData);
      expect(validResult.isValid).toBe(true);
      
      // Corrupt the CRC16
      const corruptedBrCode = validResult.brCode.slice(0, -4) + "FFFF";
      
      const parsedData = PixBRCodeGenerator.parse(corruptedBrCode);
      expect(parsedData).toBeNull();
    });
  });
});
