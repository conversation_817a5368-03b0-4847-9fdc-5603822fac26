const PixKeyValidator = require("./PixKeyValidator").default;

describe("PixKeyValidator", () => {
  describe("CPF Validation", () => {
    it("should validate correct CPF", () => {
      const result = PixKeyValidator.validateCPF("11144477735");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("CPF válido");
    });

    it("should validate formatted CPF", () => {
      const result = PixKeyValidator.validateCPF("111.444.777-35");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("CPF válido");
    });

    it("should reject CPF with wrong length", () => {
      const result = PixKeyValidator.validateCPF("1234567890");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("CPF deve conter 11 dígitos");
    });

    it("should reject CPF with all same digits", () => {
      const result = PixKeyValidator.validateCPF("11111111111");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("CPF não pode ter todos os dígitos iguais");
    });

    it("should reject CPF with invalid check digits", () => {
      const result = PixKeyValidator.validateCPF("12345678901");
      expect(result.isValid).toBe(false);
      expect(result.message).toContain("CPF inválido");
    });

    it("should reject empty CPF", () => {
      const result = PixKeyValidator.validateCPF("");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("CPF deve conter 11 dígitos");
    });
  });

  describe("Phone Validation", () => {
    it("should validate mobile phone with 11 digits", () => {
      const result = PixKeyValidator.validatePhone("11987654321");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Telefone válido");
    });

    it("should validate landline phone with 10 digits", () => {
      const result = PixKeyValidator.validatePhone("1133334444");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Telefone válido");
    });

    it("should validate formatted phone", () => {
      const result = PixKeyValidator.validatePhone("(11) 98765-4321");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Telefone válido");
    });

    it("should validate international format", () => {
      const result = PixKeyValidator.validatePhone("5511987654321");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Telefone válido");
    });

    it("should reject phone with invalid area code", () => {
      const result = PixKeyValidator.validatePhone("0987654321");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Código de área inválido");
    });

    it("should reject mobile phone not starting with 9", () => {
      const result = PixKeyValidator.validatePhone("11887654321");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Número de celular deve começar com 9 após o código de área");
    });

    it("should reject phone with wrong length", () => {
      const result = PixKeyValidator.validatePhone("123456789");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Telefone deve ter 10 ou 11 dígitos");
    });
  });

  describe("Email Validation", () => {
    it("should validate correct email", () => {
      const result = PixKeyValidator.validateEmail("<EMAIL>");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Email válido");
    });

    it("should validate email with subdomain", () => {
      const result = PixKeyValidator.validateEmail("<EMAIL>");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Email válido");
    });

    it("should validate email with special characters", () => {
      const result = PixKeyValidator.validateEmail("<EMAIL>");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Email válido");
    });

    it("should reject email without @", () => {
      const result = PixKeyValidator.validateEmail("userexample.com");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Formato de email inválido");
    });

    it("should reject email with multiple @", () => {
      const result = PixKeyValidator.validateEmail("user@@example.com");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Email deve conter exatamente um @");
    });

    it("should reject email without domain", () => {
      const result = PixKeyValidator.validateEmail("user@");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Formato de email inválido");
    });

    it("should reject very long email", () => {
      const longEmail = "a".repeat(250) + "@example.com";
      const result = PixKeyValidator.validateEmail(longEmail);
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Email muito longo (máximo 254 caracteres)");
    });
  });

  describe("Random Key Validation", () => {
    it("should validate UUID format", () => {
      const result = PixKeyValidator.validateRandomKey("550e8400-e29b-41d4-a716-************");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Chave aleatória válida (formato UUID)");
    });

    it("should validate random alphanumeric key", () => {
      const result = PixKeyValidator.validateRandomKey("abc123def456");
      expect(result.isValid).toBe(true);
      expect(result.message).toBe("Chave aleatória válida");
    });

    it("should reject key too short", () => {
      const result = PixKeyValidator.validateRandomKey("abc123");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Chave aleatória deve ter pelo menos 10 caracteres");
    });

    it("should reject key too long", () => {
      const longKey = "a".repeat(78);
      const result = PixKeyValidator.validateRandomKey(longKey);
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Chave aleatória muito longa (máximo 77 caracteres)");
    });

    it("should reject key with invalid characters", () => {
      const result = PixKeyValidator.validateRandomKey("abc123<>{}");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Chave aleatória contém caracteres inválidos");
    });
  });

  describe("General Validation", () => {
    it("should validate different key types", () => {
      const testCases: Array<{ keyType: PixKeyType; value: string; expected: boolean }> = [
        { keyType: "cpf", value: "11144477735", expected: true },
        { keyType: "phone", value: "11987654321", expected: true },
        { keyType: "email", value: "<EMAIL>", expected: true },
        { keyType: "random", value: "abc123def456", expected: true }
      ];

      testCases.forEach(({ keyType, value, expected }) => {
        const result = PixKeyValidator.validate(keyType, value);
        expect(result.isValid).toBe(expected);
      });
    });

    it("should reject empty values", () => {
      const keyTypes: PixKeyType[] = ["cpf", "phone", "email", "random"];
      
      keyTypes.forEach(keyType => {
        const result = PixKeyValidator.validate(keyType, "");
        expect(result.isValid).toBe(false);
      });
    });

    it("should reject whitespace-only values", () => {
      const result = PixKeyValidator.validate("email", "   ");
      expect(result.isValid).toBe(false);
    });

    it("should handle invalid key type", () => {
      const result = PixKeyValidator.validate("invalid" as PixKeyType, "test");
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Tipo de chave PIX inválido");
    });
  });

  describe("Key Formatting", () => {
    it("should format CPF correctly", () => {
      const formatted = PixKeyValidator.formatKey("cpf", "11144477735");
      expect(formatted).toBe("111.444.777-35");
    });

    it("should format mobile phone correctly", () => {
      const formatted = PixKeyValidator.formatKey("phone", "11987654321");
      expect(formatted).toBe("(11) 98765-4321");
    });

    it("should format landline phone correctly", () => {
      const formatted = PixKeyValidator.formatKey("phone", "1133334444");
      expect(formatted).toBe("(11) 3333-4444");
    });

    it("should not format email", () => {
      const email = "<EMAIL>";
      const formatted = PixKeyValidator.formatKey("email", email);
      expect(formatted).toBe(email);
    });

    it("should not format random key", () => {
      const randomKey = "abc123def456";
      const formatted = PixKeyValidator.formatKey("random", randomKey);
      expect(formatted).toBe(randomKey);
    });

    it("should handle invalid CPF length", () => {
      const formatted = PixKeyValidator.formatKey("cpf", "123456");
      expect(formatted).toBe("123456");
    });

    it("should handle invalid phone length", () => {
      const formatted = PixKeyValidator.formatKey("phone", "123456");
      expect(formatted).toBe("123456");
    });
  });
});
