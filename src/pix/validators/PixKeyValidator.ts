import { Pix<PERSON>eyType, PixKeyValidationResult, PixValidationOptions } from "../../types";

/**
 * PIX Key Validator
 * Implements validation algorithms for different types of PIX keys
 * according to Brazilian Central Bank specifications
 */
export default class PixKeyValidator {
  /**
   * Validates a PIX key based on its type
   * @param keyType - Type of PIX key (cpf, phone, email, random)
   * @param value - The key value to validate
   * @param options - Validation options
   * @returns Validation result with status and message
   */
  static validate(
    keyType: PixKeyType,
    value: string,
    options: PixValidationOptions = {}
  ): PixKeyValidationResult {
    if (!value || !value.trim()) {
      return {
        isValid: false,
        message: "Chave PIX é obrigatória"
      };
    }

    const trimmedValue = value.trim();

    switch (keyType) {
      case "cpf":
        return this.validateCPF(trimmedValue, options);
      case "phone":
        return this.validatePhone(trimmedValue, options);
      case "email":
        return this.validateEmail(trimmedValue, options);
      case "random":
        return this.validateRandomKey(trimmedValue, options);
      default:
        return {
          isValid: false,
          message: "Tipo de chave PIX inválido"
        };
    }
  }

  /**
   * Validates CPF (Brazilian individual taxpayer registry)
   * Implements the official CPF validation algorithm with check digits
   * @param cpf - CPF string to validate
   * @param options - Validation options
   * @returns Validation result
   */
  static validateCPF(cpf: string, options: PixValidationOptions = {}): PixKeyValidationResult {
    // Remove all non-digit characters
    const cleanCpf = cpf.replace(/\D/g, "");

    // Check length
    if (cleanCpf.length !== 11) {
      return {
        isValid: false,
        message: "CPF deve conter 11 dígitos"
      };
    }

    // Check for repeated digits (invalid CPFs like 111.111.111-11)
    if (/^(\d)\1{10}$/.test(cleanCpf)) {
      return {
        isValid: false,
        message: "CPF não pode ter todos os dígitos iguais"
      };
    }

    // Validate first check digit
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
    }
    const firstDigit = ((sum * 10) % 11) % 10;

    if (firstDigit !== parseInt(cleanCpf.charAt(9))) {
      return {
        isValid: false,
        message: "CPF inválido - primeiro dígito verificador incorreto"
      };
    }

    // Validate second check digit
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
    }
    const secondDigit = ((sum * 10) % 11) % 10;

    if (secondDigit !== parseInt(cleanCpf.charAt(10))) {
      return {
        isValid: false,
        message: "CPF inválido - segundo dígito verificador incorreto"
      };
    }

    return {
      isValid: true,
      message: "CPF válido"
    };
  }

  /**
   * Validates phone number for PIX
   * Supports Brazilian mobile and landline numbers
   * @param phone - Phone number to validate
   * @param options - Validation options
   * @returns Validation result
   */
  static validatePhone(phone: string, options: PixValidationOptions = {}): PixKeyValidationResult {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, "");

    // Check for international format
    if (cleanPhone.startsWith("55")) {
      const nationalNumber = cleanPhone.substring(2);
      
      // Brazilian mobile: 11 digits (2 area code + 9 mobile number)
      // Brazilian landline: 10 digits (2 area code + 8 landline number)
      if (nationalNumber.length === 11 || nationalNumber.length === 10) {
        // Validate area code (11-99)
        const areaCode = parseInt(nationalNumber.substring(0, 2));
        if (areaCode < 11 || areaCode > 99) {
          return {
            isValid: false,
            message: "Código de área inválido"
          };
        }

        // For mobile numbers, first digit after area code should be 9
        if (nationalNumber.length === 11 && nationalNumber.charAt(2) !== "9") {
          return {
            isValid: false,
            message: "Número de celular deve começar com 9 após o código de área"
          };
        }

        return {
          isValid: true,
          message: "Telefone válido"
        };
      }
    }

    // National format validation
    if (cleanPhone.length === 10 || cleanPhone.length === 11) {
      // Validate area code
      const areaCode = parseInt(cleanPhone.substring(0, 2));
      if (areaCode < 11 || areaCode > 99) {
        return {
          isValid: false,
          message: "Código de área inválido"
        };
      }

      // For mobile numbers, first digit after area code should be 9
      if (cleanPhone.length === 11 && cleanPhone.charAt(2) !== "9") {
        return {
          isValid: false,
          message: "Número de celular deve começar com 9 após o código de área"
        };
      }

      return {
        isValid: true,
        message: "Telefone válido"
      };
    }

    return {
      isValid: false,
      message: "Telefone deve ter 10 ou 11 dígitos"
    };
  }

  /**
   * Validates email address for PIX
   * Uses RFC 5322 compliant regex pattern
   * @param email - Email address to validate
   * @param options - Validation options
   * @returns Validation result
   */
  static validateEmail(email: string, options: PixValidationOptions = {}): PixKeyValidationResult {
    // Basic email regex pattern (RFC 5322 compliant)
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!emailRegex.test(email)) {
      return {
        isValid: false,
        message: "Formato de email inválido"
      };
    }

    // Additional length validation
    if (email.length > 254) {
      return {
        isValid: false,
        message: "Email muito longo (máximo 254 caracteres)"
      };
    }

    // Check for valid domain
    const parts = email.split("@");
    if (parts.length !== 2) {
      return {
        isValid: false,
        message: "Email deve conter exatamente um @"
      };
    }

    const [localPart, domain] = parts;
    
    if (localPart.length > 64) {
      return {
        isValid: false,
        message: "Parte local do email muito longa (máximo 64 caracteres)"
      };
    }

    if (domain.length > 253) {
      return {
        isValid: false,
        message: "Domínio do email muito longo (máximo 253 caracteres)"
      };
    }

    return {
      isValid: true,
      message: "Email válido"
    };
  }

  /**
   * Validates random PIX key (UUID format)
   * Random keys are typically UUIDs generated by financial institutions
   * @param randomKey - Random key to validate
   * @param options - Validation options
   * @returns Validation result
   */
  static validateRandomKey(randomKey: string, options: PixValidationOptions = {}): PixKeyValidationResult {
    // Minimum length validation
    if (randomKey.length < 10) {
      return {
        isValid: false,
        message: "Chave aleatória deve ter pelo menos 10 caracteres"
      };
    }

    // Maximum length validation (PIX specification)
    if (randomKey.length > 77) {
      return {
        isValid: false,
        message: "Chave aleatória muito longa (máximo 77 caracteres)"
      };
    }

    // Check for valid characters (alphanumeric and some special characters)
    const validCharsRegex = /^[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]+$/;
    if (!validCharsRegex.test(randomKey)) {
      return {
        isValid: false,
        message: "Chave aleatória contém caracteres inválidos"
      };
    }

    // Optional: Check if it's a valid UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(randomKey)) {
      return {
        isValid: true,
        message: "Chave aleatória válida (formato UUID)"
      };
    }

    return {
      isValid: true,
      message: "Chave aleatória válida"
    };
  }

  /**
   * Formats a PIX key for display purposes
   * @param keyType - Type of PIX key
   * @param value - Raw key value
   * @returns Formatted key string
   */
  static formatKey(keyType: PixKeyType, value: string): string {
    switch (keyType) {
      case "cpf":
        const cleanCpf = value.replace(/\D/g, "");
        if (cleanCpf.length === 11) {
          return cleanCpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
        }
        return value;
      
      case "phone":
        const cleanPhone = value.replace(/\D/g, "");
        if (cleanPhone.length === 11) {
          return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
        } else if (cleanPhone.length === 10) {
          return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3");
        }
        return value;
      
      case "email":
      case "random":
      default:
        return value;
    }
  }
}
