/**
 * PIX Module for QR Code Styling
 * 
 * This module provides comprehensive PIX (Brazilian Instant Payment System) functionality
 * including BR Code generation, PIX key validation, and payload formatting according to
 * Brazilian Central Bank specifications.
 * 
 * <AUTHOR> Code Styling Team
 * @version 1.0.0
 */

// Core PIX functionality
export { default as Pix<PERSON>CodeGenerator } from "./core/PixBRCodeGenerator";
import PixBRCodeGeneratorClass from "./core/PixBRCodeGenerator";

// Validators
export { default as Pix<PERSON><PERSON>Validator } from "./validators/PixKeyValidator";
import PixKeyValidatorClass from "./validators/PixKeyValidator";

// Utilities
export { default as PixFormatter } from "./utils/PixFormatter";
export { default as CR<PERSON>16Calculator } from "./utils/CRC16Calculator";
import CRC16CalculatorClass from "./utils/CRC16Calculator";

// Re-export PIX types for convenience
export type {
  Pix<PERSON>eyType,
  PixKeyValidationResult,
  Pix<PERSON><PERSON>,
  <PERSON>x<PERSON>ayloadOptions,
  PixBRCodeResult,
  PixValidationOptions,
  PixKeyValidators
} from "../types";

/**
 * PIX Constants
 * Common constants used throughout the PIX module
 */
export const PIX_CONSTANTS = {
  // PIX System Identifier
  PIX_IDENTIFIER: "br.gov.bcb.pix",
  
  // Currency and Country
  CURRENCY_CODE_BRL: "986",
  COUNTRY_CODE_BRAZIL: "BR",
  
  // Default Values
  DEFAULT_MERCHANT_CATEGORY_CODE: "0000",
  DEFAULT_REFERENCE: "***",
  
  // Limits
  MAX_PAYLOAD_LENGTH: 512,
  MIN_PAYLOAD_LENGTH: 50,
  MAX_RECEIVER_NAME_LENGTH: 25,
  MAX_RECEIVER_CITY_LENGTH: 15,
  MAX_DESCRIPTION_LENGTH: 50,
  MAX_REFERENCE_LENGTH: 25,
  
  // Key Type Limits
  CPF_LENGTH: 11,
  MAX_PHONE_LENGTH: 15,
  MAX_EMAIL_LENGTH: 254,
  MAX_RANDOM_KEY_LENGTH: 77,
  MIN_RANDOM_KEY_LENGTH: 10
} as const;

/**
 * PIX Key Types
 * Supported PIX key types with their identifiers
 */
export const PIX_KEY_TYPES = {
  CPF: "cpf",
  PHONE: "phone", 
  EMAIL: "email",
  RANDOM: "random"
} as const;

/**
 * PIX Validation Messages
 * Standard validation messages for PIX operations
 */
export const PIX_VALIDATION_MESSAGES = {
  // General
  REQUIRED_FIELD: "Campo obrigatório",
  INVALID_FORMAT: "Formato inválido",
  
  // CPF
  CPF_INVALID_LENGTH: "CPF deve conter 11 dígitos",
  CPF_REPEATED_DIGITS: "CPF não pode ter todos os dígitos iguais",
  CPF_INVALID_CHECK_DIGIT: "CPF inválido - dígito verificador incorreto",
  CPF_VALID: "CPF válido",
  
  // Phone
  PHONE_INVALID_LENGTH: "Telefone deve ter 10 ou 11 dígitos",
  PHONE_INVALID_AREA_CODE: "Código de área inválido",
  PHONE_INVALID_MOBILE_FORMAT: "Número de celular deve começar com 9 após o código de área",
  PHONE_VALID: "Telefone válido",
  
  // Email
  EMAIL_INVALID_FORMAT: "Formato de email inválido",
  EMAIL_TOO_LONG: "Email muito longo",
  EMAIL_INVALID_DOMAIN: "Domínio do email inválido",
  EMAIL_VALID: "Email válido",
  
  // Random Key
  RANDOM_KEY_TOO_SHORT: "Chave aleatória deve ter pelo menos 10 caracteres",
  RANDOM_KEY_TOO_LONG: "Chave aleatória muito longa",
  RANDOM_KEY_INVALID_CHARS: "Chave aleatória contém caracteres inválidos",
  RANDOM_KEY_VALID: "Chave aleatória válida",
  
  // Payload
  PAYLOAD_EMPTY: "Payload vazio",
  PAYLOAD_TOO_LONG: "Payload muito longo",
  PAYLOAD_TOO_SHORT: "Payload muito curto",
  PAYLOAD_INVALID_CRC: "CRC16 inválido",
  PAYLOAD_VALID: "Payload válido"
} as const;

/**
 * Convenience function to generate PIX BR Code
 * Simplified interface for common use cases
 * 
 * @param pixData - PIX transaction data
 * @param options - Optional generation options
 * @returns PIX BR Code result
 * 
 * @example
 * ```typescript
 * import { generatePixBRCode } from 'qr-code-styling/pix';
 * 
 * const result = generatePixBRCode({
 *   keyType: 'email',
 *   pixKey: '<EMAIL>',
 *   receiverName: 'João Silva',
 *   receiverCity: 'São Paulo',
 *   amount: 100.50,
 *   description: 'Pagamento de serviços'
 * });
 * 
 * if (result.isValid) {
 *   console.log('BR Code:', result.brCode);
 * } else {
 *   console.error('Error:', result.error);
 * }
 * ```
 */
export function generatePixBRCode(
  pixData: any,
  options?: any
): any {
  return PixBRCodeGeneratorClass.generate(pixData, options);
}

/**
 * Convenience function to validate PIX key
 * Simplified interface for PIX key validation
 * 
 * @param keyType - Type of PIX key
 * @param keyValue - PIX key value
 * @param options - Optional validation options
 * @returns Validation result
 * 
 * @example
 * ```typescript
 * import { validatePixKey } from 'qr-code-styling/pix';
 * 
 * const result = validatePixKey('cpf', '12345678901');
 * 
 * if (result.isValid) {
 *   console.log('Valid PIX key:', result.message);
 * } else {
 *   console.error('Invalid PIX key:', result.message);
 * }
 * ```
 */
export function validatePixKey(
  keyType: any,
  keyValue: string,
  options?: any
): any {
  return PixKeyValidatorClass.validate(keyType, keyValue, options);
}

/**
 * Convenience function to parse PIX BR Code
 * Extracts PIX data from an existing BR Code
 * 
 * @param brCode - BR Code string to parse
 * @returns Parsed PIX data or null if invalid
 * 
 * @example
 * ```typescript
 * import { parsePixBRCode } from 'qr-code-styling/pix';
 * 
 * const pixData = parsePixBRCode('00020101021226580014br.gov.bcb.pix...');
 * 
 * if (pixData) {
 *   console.log('PIX Key:', pixData.pixKey);
 *   console.log('Amount:', pixData.amount);
 * } else {
 *   console.error('Invalid BR Code');
 * }
 * ```
 */
export function parsePixBRCode(brCode: string): any | null {
  return PixBRCodeGeneratorClass.parse(brCode);
}

/**
 * Convenience function to calculate CRC16
 * Calculates CRC16 checksum for any string
 * 
 * @param data - Data string to calculate CRC16 for
 * @returns CRC16 checksum as uppercase hexadecimal string
 * 
 * @example
 * ```typescript
 * import { calculateCRC16 } from 'qr-code-styling/pix';
 * 
 * const crc = calculateCRC16('00020101021226580014br.gov.bcb.pix');
 * console.log('CRC16:', crc); // e.g., "A1B2"
 * ```
 */
export function calculateCRC16(data: string): string {
  return CRC16CalculatorClass.calculate(data);
}

/**
 * Convenience function to validate CRC16
 * Validates CRC16 checksum of a payload
 * 
 * @param payload - Complete payload with CRC16
 * @returns True if CRC16 is valid
 * 
 * @example
 * ```typescript
 * import { validateCRC16 } from 'qr-code-styling/pix';
 * 
 * const isValid = validateCRC16('00020101021226580014br.gov.bcb.pixA1B2');
 * console.log('CRC16 valid:', isValid);
 * ```
 */
export function validateCRC16(payload: string): boolean {
  return CRC16CalculatorClass.validate(payload);
}

/**
 * PIX Module Information
 * Metadata about the PIX module
 */
export const PIX_MODULE_INFO = {
  name: "PIX Module for QR Code Styling",
  version: "1.0.0",
  description: "Comprehensive PIX (Brazilian Instant Payment System) functionality",
  author: "QR Code Styling Team",
  specification: "Brazilian Central Bank PIX Technical Specification",
  features: [
    "PIX BR Code generation",
    "PIX key validation (CPF, Phone, Email, Random)",
    "CRC16 calculation and validation",
    "Payload parsing and formatting",
    "Full compliance with Central Bank specifications"
  ]
} as const;
