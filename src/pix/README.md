# PIX Module for QR Code Styling

Este módulo fornece funcionalidades completas para o sistema PIX (Sistema de Pagamentos Instantâneos) brasileiro, incluindo geração de códigos BR Code, validação de chaves PIX e formatação de dados conforme as especificações do Banco Central do Brasil.

## Características

- ✅ **Geração de BR Code**: Criação completa de códigos PIX conforme especificação técnica
- ✅ **Validação de Chaves PIX**: Suporte para CPF, telefone, email e chaves aleatórias
- ✅ **Cálculo CRC16**: Implementação do algoritmo CRC16-CCITT para validação de integridade
- ✅ **Formatação de Dados**: Normalização e formatação automática de dados PIX
- ✅ **Parsing de BR Code**: Extração de dados PIX de códigos existentes
- ✅ **TypeScript**: Tipagem completa e interfaces bem definidas
- ✅ **Testes Unitários**: Cobertura completa de testes para todas as funcionalidades

## Instalação

```bash
npm install qr-code-styling
```

## Uso Básico

### Geração de BR Code

```typescript
import { generatePixBRCode } from 'qr-code-styling';

const pixData = {
  keyType: 'email',
  pixKey: '<EMAIL>',
  receiverName: 'João Silva',
  receiverCity: 'São Paulo',
  amount: 100.50,
  description: 'Pagamento de serviços'
};

const result = generatePixBRCode(pixData);

if (result.isValid) {
  console.log('BR Code:', result.brCode);
} else {
  console.error('Erro:', result.error);
}
```

### Validação de Chave PIX

```typescript
import { validatePixKey } from 'qr-code-styling';

// Validar CPF
const cpfResult = validatePixKey('cpf', '11144477735');
console.log(cpfResult.isValid); // true/false
console.log(cpfResult.message); // Mensagem de validação

// Validar telefone
const phoneResult = validatePixKey('phone', '11987654321');

// Validar email
const emailResult = validatePixKey('email', '<EMAIL>');

// Validar chave aleatória
const randomResult = validatePixKey('random', '550e8400-e29b-41d4-a716-************');
```

### Parsing de BR Code

```typescript
import { parsePixBRCode } from 'qr-code-styling';

const brCode = '00020101021226580014br.gov.bcb.pix...';
const pixData = parsePixBRCode(brCode);

if (pixData) {
  console.log('Chave PIX:', pixData.pixKey);
  console.log('Valor:', pixData.amount);
  console.log('Recebedor:', pixData.receiverName);
} else {
  console.error('BR Code inválido');
}
```

## API Completa

### Classes Principais

#### `PixBRCodeGenerator`

Classe principal para geração e parsing de códigos BR Code.

```typescript
import { PixBRCodeGenerator } from 'qr-code-styling';

// Gerar BR Code
const result = PixBRCodeGenerator.generate(pixData, options);

// Fazer parsing de BR Code
const parsedData = PixBRCodeGenerator.parse(brCode);
```

#### `PixKeyValidator`

Validador de chaves PIX com suporte a todos os tipos.

```typescript
import { PixKeyValidator } from 'qr-code-styling';

// Validar chave
const result = PixKeyValidator.validate(keyType, keyValue, options);

// Formatar chave para exibição
const formatted = PixKeyValidator.formatKey(keyType, keyValue);
```

#### `CRC16Calculator`

Calculadora de CRC16 para validação de integridade.

```typescript
import { CRC16Calculator } from 'qr-code-styling';

// Calcular CRC16
const crc = CRC16Calculator.calculate(data);

// Validar CRC16
const isValid = CRC16Calculator.validate(payload);

// Adicionar CRC16 ao payload
const completePayload = CRC16Calculator.appendCRC(payload);
```

#### `PixFormatter`

Utilitários de formatação e normalização de dados PIX.

```typescript
import { PixFormatter } from 'qr-code-styling';

// Formatar texto
const formatted = PixFormatter.formatText('João da Silva');

// Criar valor TLV
const tlv = PixFormatter.getValue('01', 'valor');

// Sanitizar dados PIX
const sanitized = PixFormatter.sanitizePixData(rawData);
```

## Tipos de Chave PIX Suportados

### CPF
- Formato: 11 dígitos numéricos
- Validação: Algoritmo oficial com dígitos verificadores
- Exemplo: `11144477735`

### Telefone
- Formato: Número brasileiro com código do país
- Validação: Código de área válido e formato correto
- Exemplo: `+*************`

### Email
- Formato: Endereço de email válido (RFC 5322)
- Validação: Formato e comprimento
- Exemplo: `<EMAIL>`

### Chave Aleatória
- Formato: String alfanumérica (UUID recomendado)
- Validação: Comprimento e caracteres permitidos
- Exemplo: `550e8400-e29b-41d4-a716-************`

## Estrutura do BR Code

O BR Code gerado segue a especificação técnica do Banco Central:

```
00 - Payload Format Indicator (01)
01 - Point of Initiation Method (11/12)
26 - Merchant Account Information
  00 - GUI (br.gov.bcb.pix)
  01 - PIX Key
  02 - Description (opcional)
52 - Merchant Category Code (0000)
53 - Transaction Currency (986 - BRL)
54 - Transaction Amount (opcional)
58 - Country Code (BR)
59 - Merchant Name
60 - Merchant City
62 - Additional Data Field Template
  05 - Reference/Transaction ID
63 - CRC16
```

## Constantes e Configurações

```typescript
import { PIX_CONSTANTS, PIX_KEY_TYPES } from 'qr-code-styling';

// Constantes PIX
console.log(PIX_CONSTANTS.PIX_IDENTIFIER); // 'br.gov.bcb.pix'
console.log(PIX_CONSTANTS.CURRENCY_CODE_BRL); // '986'
console.log(PIX_CONSTANTS.COUNTRY_CODE_BRAZIL); // 'BR'

// Tipos de chave
console.log(PIX_KEY_TYPES.CPF); // 'cpf'
console.log(PIX_KEY_TYPES.EMAIL); // 'email'
```

## Tratamento de Erros

Todas as funções retornam objetos com status de validação:

```typescript
interface PixBRCodeResult {
  brCode: string;
  isValid: boolean;
  error?: string;
}

interface PixKeyValidationResult {
  isValid: boolean;
  message: string;
}
```

## Limitações e Especificações

- **Payload máximo**: 512 caracteres
- **Nome do recebedor**: Máximo 25 caracteres
- **Cidade do recebedor**: Máximo 15 caracteres
- **Descrição**: Máximo 50 caracteres
- **Referência**: Máximo 25 caracteres (apenas alfanuméricos)
- **Valor**: Máximo 2 casas decimais

## Compatibilidade

- ✅ Node.js 18+
- ✅ TypeScript 5+
- ✅ Browsers modernos
- ✅ React, Vue, Angular
- ✅ Next.js, Nuxt.js

## Contribuição

Para contribuir com o módulo PIX:

1. Fork o repositório
2. Crie uma branch para sua feature
3. Implemente testes para novas funcionalidades
4. Execute os testes: `npm test`
5. Submeta um Pull Request

## Licença

MIT License - veja o arquivo LICENSE para detalhes.

## Suporte

Para dúvidas ou problemas relacionados ao módulo PIX:

- Abra uma issue no GitHub
- Consulte a documentação do Banco Central do Brasil
- Verifique os testes unitários para exemplos de uso

---

**Nota**: Este módulo implementa as especificações técnicas do PIX conforme definidas pelo Banco Central do Brasil. Para informações oficiais, consulte o Manual de Padrões para Iniciação do PIX.
