# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Build and Development
- `npm run build` - Production build using webpack (outputs to lib/)
- `npm run build:dev` - Development build using webpack
- `npm run dev` or `npm start` - Start webpack dev server with live reload
- `npm test` - Run Jest test suite with coverage

### Linting and Type Checking
- ESLint is configured via `eslint.config.mjs` and runs automatically during builds
- TypeScript compilation is handled by webpack via ts-loader
- Use `npm run build` to check for both linting and TypeScript compilation errors

## Architecture

### Core Structure
This is a TypeScript library for generating styled QR codes with image support. The main entry point is `src/index.ts` which exports the `QRCodeStyling` class and various constants.

**Key Components:**
- `QRCodeStyling` (src/core/QRCodeStyling.ts) - Main class that handles QR code generation, styling, and rendering
- `QRSVG` (src/core/QRSVG.ts) - SVG rendering engine
- Constants (src/constants/) - Type definitions for dots, corners, error correction, etc.
- Tools (src/tools/) - Utility functions for image processing, validation, and data manipulation
- Figures (src/figures/) - Shape rendering components for dots and corners

### PIX Integration
The library includes comprehensive Brazilian PIX payment system support:
- Core generator in `src/pix/core/PixBRCodeGenerator.ts`
- Key validation in `src/pix/validators/PixKeyValidator.ts` 
- Formatting utilities in `src/pix/utils/PixFormatter.ts`
- CRC16 checksum calculation in `src/pix/utils/CRC16Calculator.ts`

### Rendering System
The library supports dual rendering modes:
- **Canvas mode**: Uses HTML5 Canvas for pixel-perfect raster output
- **SVG mode**: Generates scalable vector graphics

Both modes use the same underlying QR data structure but different rendering pipelines. The QRCodeStyling class coordinates between QR code generation (via qrcode-generator library), styling configuration, and the appropriate renderer.

### Node.js Support
The library supports server-side rendering through:
- `nodeCanvas` option for Canvas API in Node.js (requires node-canvas)
- `jsdom` option for DOM manipulation in Node.js environments

### Build System
- Webpack configuration split across three files:
  - `webpack.config.common.js` - Base configuration
  - `webpack.config.build.js` - Production/development builds
  - `webpack.config.dev-server.js` - Development server with live reload
- Outputs both UMD bundle (qr-code-styling.js) and CommonJS version (qr-code-styling.common.js)
- TypeScript definitions generated automatically to lib/

### Testing
- Jest configuration in `jest.config.js` uses ts-jest preset
- Tests use jsdom environment for DOM-dependent functionality
- Coverage collection enabled for all TypeScript source files
- Test files follow `*.test.js` pattern throughout the codebase