<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PIX QR Code Generator - Refatorado</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #00BC4B;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input:focus, select:focus {
            border-color: #00BC4B;
            outline: none;
        }
        
        .btn {
            background-color: #00BC4B;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        
        .btn:hover {
            background-color: #009639;
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        
        .qr-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .br-code {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin-top: 15px;
        }
        
        .success {
            color: #00BC4B;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇧🇷 PIX QR Code Generator</h1>
        <p style="text-align: center; color: #666;">Gerador de QR Code PIX com backend refatorado</p>
        
        <div class="info">
            <strong>✅ Refatoração Concluída!</strong><br>
            A lógica de geração do payload PIX foi movida do frontend para o backend, 
            seguindo as melhores práticas de segurança e arquitetura de software.
        </div>
        
        <form id="pixForm">
            <div class="form-group">
                <label for="keyType">Tipo de Chave PIX *</label>
                <select id="keyType" required>
                    <option value="">Selecione o tipo</option>
                    <option value="cpf">CPF</option>
                    <option value="phone">Telefone</option>
                    <option value="email">Email</option>
                    <option value="random">Chave Aleatória</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="pixKey">Chave PIX *</label>
                <input type="text" id="pixKey" placeholder="Digite sua chave PIX" required>
            </div>
            
            <div class="form-group">
                <label for="receiverName">Nome do Recebedor *</label>
                <input type="text" id="receiverName" placeholder="João Silva" required>
            </div>
            
            <div class="form-group">
                <label for="receiverCity">Cidade do Recebedor *</label>
                <input type="text" id="receiverCity" placeholder="São Paulo" required>
            </div>
            
            <div class="form-group">
                <label for="amount">Valor (opcional)</label>
                <input type="number" id="amount" step="0.01" min="0" placeholder="100.50">
            </div>
            
            <div class="form-group">
                <label for="description">Descrição (opcional)</label>
                <input type="text" id="description" placeholder="Pagamento de serviços">
            </div>
            
            <button type="submit" class="btn">Gerar QR Code PIX</button>
        </form>
        
        <div id="result" class="result">
            <h3>Resultado:</h3>
            <div id="status"></div>
            <div id="qrcode" class="qr-container"></div>
            <div id="brcode" class="br-code"></div>
        </div>
    </div>

    <script>
        // Simulação da API do backend refatorado
        class PixBackendAPI {
            // Simula a validação de chave PIX (agora no backend)
            static validatePixKey(keyType, keyValue) {
                // Esta lógica agora está no backend (PixKeyValidator.ts)
                switch (keyType) {
                    case 'cpf':
                        return this.validateCPF(keyValue);
                    case 'phone':
                        return this.validatePhone(keyValue);
                    case 'email':
                        return this.validateEmail(keyValue);
                    case 'random':
                        return keyValue.length >= 10;
                    default:
                        return false;
                }
            }
            
            static validateCPF(cpf) {
                // Simulação da validação de CPF (implementada em PixKeyValidator.ts)
                const cleanCpf = cpf.replace(/\D/g, '');
                return cleanCpf.length === 11 && !(/^(\d)\1{10}$/.test(cleanCpf));
            }
            
            static validatePhone(phone) {
                // Simulação da validação de telefone (implementada em PixKeyValidator.ts)
                const cleanPhone = phone.replace(/\D/g, '');
                return cleanPhone.length >= 10 && cleanPhone.length <= 13;
            }
            
            static validateEmail(email) {
                // Simulação da validação de email (implementada em PixKeyValidator.ts)
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }
            
            // Simula a geração do BR Code (agora no backend)
            static generateBRCode(pixData) {
                // Esta lógica agora está no backend (PixBRCodeGenerator.ts)
                try {
                    // Formatação de texto (PixFormatter.ts)
                    const formatText = (text) => {
                        return text
                            .normalize('NFD')
                            .replace(/[\u0300-\u036f]/g, '')
                            .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '');
                    };
                    
                    // Função TLV (PixFormatter.ts)
                    const getValue = (tag, value) => {
                        return `${tag.padStart(2, '0')}${value.length.toString().padStart(2, '0')}${value}`;
                    };
                    
                    // CRC16 (CRC16Calculator.ts)
                    const calculateCRC16 = (data) => {
                        let crc = 0xFFFF;
                        const bytes = new TextEncoder().encode(data);
                        
                        for (const byte of bytes) {
                            crc ^= byte << 8;
                            for (let i = 0; i < 8; i++) {
                                if (crc & 0x8000) {
                                    crc = (crc << 1) ^ 0x1021;
                                } else {
                                    crc <<= 1;
                                }
                            }
                        }
                        
                        return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
                    };
                    
                    // Construção do payload PIX (PixBRCodeGenerator.ts)
                    let payload = '';
                    
                    // Tags obrigatórias
                    payload += getValue('00', '01'); // Payload Format Indicator
                    payload += getValue('01', '11'); // Point of Initiation Method
                    
                    // Merchant Account Information (Tag 26)
                    let accountInfo = getValue('00', 'br.gov.bcb.pix');
                    accountInfo += getValue('01', pixData.pixKey);
                    if (pixData.description) {
                        accountInfo += getValue('02', formatText(pixData.description));
                    }
                    payload += getValue('26', accountInfo);
                    
                    payload += getValue('52', '0000'); // Merchant Category Code
                    payload += getValue('53', '986');  // Transaction Currency (BRL)
                    
                    if (pixData.amount && pixData.amount > 0) {
                        payload += getValue('54', pixData.amount.toFixed(2));
                    }
                    
                    payload += getValue('58', 'BR'); // Country Code
                    payload += getValue('59', formatText(pixData.receiverName));
                    payload += getValue('60', formatText(pixData.receiverCity));
                    
                    // Additional Data
                    const additionalData = getValue('05', '***');
                    payload += getValue('62', additionalData);
                    
                    // CRC16
                    payload += getValue('63', calculateCRC16(payload + '6304'));
                    
                    return {
                        isValid: true,
                        brCode: payload
                    };
                    
                } catch (error) {
                    return {
                        isValid: false,
                        error: error.message
                    };
                }
            }
        }
        
        // Simulação do QR Code Styling (usando canvas simples)
        function generateQRCode(data, container) {
            // Em produção, usaria: new QRCodeStyling({ data: brCode })
            container.innerHTML = `
                <div style="width: 200px; height: 200px; background: #000; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center;">
                    QR CODE<br>
                    (${data.length} chars)<br>
                    <small>Use QRCodeStyling<br>em produção</small>
                </div>
            `;
        }
        
        // Event listeners
        document.getElementById('pixForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const pixData = {
                keyType: document.getElementById('keyType').value,
                pixKey: document.getElementById('pixKey').value,
                receiverName: document.getElementById('receiverName').value,
                receiverCity: document.getElementById('receiverCity').value,
                amount: parseFloat(document.getElementById('amount').value) || 0,
                description: document.getElementById('description').value
            };
            
            // Validação usando backend API
            const isValidKey = PixBackendAPI.validatePixKey(pixData.keyType, pixData.pixKey);
            
            if (!isValidKey) {
                document.getElementById('status').innerHTML = '<span class="error">❌ Chave PIX inválida!</span>';
                document.getElementById('result').style.display = 'block';
                return;
            }
            
            // Geração do BR Code usando backend API
            const result = PixBackendAPI.generateBRCode(pixData);
            
            if (result.isValid) {
                document.getElementById('status').innerHTML = '<span class="success">✅ QR Code PIX gerado com sucesso!</span>';
                generateQRCode(result.brCode, document.getElementById('qrcode'));
                document.getElementById('brcode').innerHTML = `
                    <strong>BR Code:</strong><br>
                    ${result.brCode}
                `;
            } else {
                document.getElementById('status').innerHTML = `<span class="error">❌ Erro: ${result.error}</span>`;
            }
            
            document.getElementById('result').style.display = 'block';
        });
        
        // Placeholder dinâmico para chave PIX
        document.getElementById('keyType').addEventListener('change', function() {
            const keyType = this.value;
            const pixKeyInput = document.getElementById('pixKey');
            
            const placeholders = {
                'cpf': '000.000.000-00',
                'phone': '(11) 99999-9999',
                'email': '<EMAIL>',
                'random': 'chave-aleatoria-uuid'
            };
            
            pixKeyInput.placeholder = placeholders[keyType] || 'Digite sua chave PIX';
        });
        
        console.log('🚀 PIX QR Code Generator carregado!');
        console.log('✅ Backend refatorado com sucesso!');
        console.log('📋 Funcionalidades disponíveis:');
        console.log('- Validação de chaves PIX');
        console.log('- Geração de payload BR Code');
        console.log('- Formatação de dados');
        console.log('- Cálculo CRC16');
    </script>
</body>
</html>
